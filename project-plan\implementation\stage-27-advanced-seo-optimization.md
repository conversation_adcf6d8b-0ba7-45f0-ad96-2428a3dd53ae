# Stage 27: Advanced SEO Optimization

## Stage Overview
Implement comprehensive SEO optimization following Google Search Central guidelines, including advanced meta tags, Open Graph, Twitter Cards, favicon optimization, and Core Web Vitals optimization.

## Prerequisites
- Stages 1-26 completed successfully
- Schema markup system operational
- Static site generation working
- Performance optimization implemented

## Implementation Steps

### Step 27.1: Create SEO Meta Manager Service

#### manager/wp-content/themes/universal-theme/src/Services/SEOMetaService.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Theme\Services;

/**
 * SEO Meta Manager Service
 * 
 * Handles advanced SEO meta tags following Google Search Central guidelines
 * 
 * @package UniversalApp\Theme\Services
 * @since 1.0.0
 */
final class SEOMetaService
{
    private array $config;
    private string $siteUrl;
    private string $siteName;

    public function __construct()
    {
        $this->siteUrl = get_option('universal_static_site_url', home_url());
        $this->siteName = get_bloginfo('name');
        $this->loadConfig();
    }

    /**
     * Generate complete meta tags for a page
     */
    public function generateMetaTags(array $pageData): string
    {
        $meta = [];
        
        // Basic meta tags
        $meta[] = $this->generateBasicMeta($pageData);
        
        // Open Graph meta tags
        $meta[] = $this->generateOpenGraphMeta($pageData);
        
        // Twitter Card meta tags
        $meta[] = $this->generateTwitterCardMeta($pageData);
        
        // Google-specific meta tags
        $meta[] = $this->generateGoogleMeta($pageData);
        
        // Canonical and alternate URLs
        $meta[] = $this->generateCanonicalMeta($pageData);
        
        // Robots and indexing directives
        $meta[] = $this->generateRobotsMeta($pageData);
        
        return implode("\n", array_filter($meta));
    }

    /**
     * Generate basic meta tags
     */
    private function generateBasicMeta(array $pageData): string
    {
        $title = $this->optimizeTitle($pageData['title'] ?? '');
        $description = $this->optimizeDescription($pageData['description'] ?? '');
        $keywords = $this->generateKeywords($pageData);

        return sprintf(
            '<title>%s</title>
<meta name="description" content="%s">
<meta name="keywords" content="%s">
<meta name="author" content="%s">
<meta name="generator" content="Universal App v1.0.0">',
            esc_html($title),
            esc_attr($description),
            esc_attr(implode(', ', $keywords)),
            esc_attr($this->siteName)
        );
    }

    /**
     * Generate Open Graph meta tags
     */
    private function generateOpenGraphMeta(array $pageData): string
    {
        $ogType = $pageData['type'] ?? 'website';
        $ogTitle = $this->optimizeTitle($pageData['title'] ?? '', 60);
        $ogDescription = $this->optimizeDescription($pageData['description'] ?? '', 300);
        $ogImage = $pageData['image'] ?? $this->getDefaultImage();
        $ogUrl = $pageData['url'] ?? '';

        return sprintf(
            '<meta property="og:type" content="%s">
<meta property="og:title" content="%s">
<meta property="og:description" content="%s">
<meta property="og:image" content="%s">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:image:alt" content="%s">
<meta property="og:url" content="%s">
<meta property="og:site_name" content="%s">
<meta property="og:locale" content="en_US">',
            esc_attr($ogType),
            esc_attr($ogTitle),
            esc_attr($ogDescription),
            esc_url($ogImage),
            esc_attr($ogTitle),
            esc_url($ogUrl),
            esc_attr($this->siteName)
        );
    }

    /**
     * Generate Twitter Card meta tags
     */
    private function generateTwitterCardMeta(array $pageData): string
    {
        $cardType = $pageData['twitter_card'] ?? 'summary_large_image';
        $twitterTitle = $this->optimizeTitle($pageData['title'] ?? '', 70);
        $twitterDescription = $this->optimizeDescription($pageData['description'] ?? '', 200);
        $twitterImage = $pageData['image'] ?? $this->getDefaultImage();
        $twitterSite = $this->config['twitter_handle'] ?? '';

        return sprintf(
            '<meta name="twitter:card" content="%s">
<meta name="twitter:title" content="%s">
<meta name="twitter:description" content="%s">
<meta name="twitter:image" content="%s">
<meta name="twitter:image:alt" content="%s">
<meta name="twitter:site" content="%s">
<meta name="twitter:creator" content="%s">',
            esc_attr($cardType),
            esc_attr($twitterTitle),
            esc_attr($twitterDescription),
            esc_url($twitterImage),
            esc_attr($twitterTitle),
            esc_attr($twitterSite),
            esc_attr($twitterSite)
        );
    }

    /**
     * Generate Google-specific meta tags
     */
    private function generateGoogleMeta(array $pageData): string
    {
        $googleSiteVerification = $this->config['google_site_verification'] ?? '';
        $googlebot = $pageData['googlebot'] ?? 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1';

        $meta = sprintf(
            '<meta name="googlebot" content="%s">
<meta name="google" content="notranslate">',
            esc_attr($googlebot)
        );

        if ($googleSiteVerification) {
            $meta .= sprintf(
                '<meta name="google-site-verification" content="%s">',
                esc_attr($googleSiteVerification)
            );
        }

        // Add news-specific meta if it's a news article
        if (($pageData['type'] ?? '') === 'article') {
            $meta .= $this->generateNewsMeta($pageData);
        }

        return $meta;
    }

    /**
     * Generate canonical and alternate URLs
     */
    private function generateCanonicalMeta(array $pageData): string
    {
        $canonical = $pageData['canonical'] ?? $pageData['url'] ?? '';
        $alternates = $pageData['alternates'] ?? [];

        $meta = sprintf('<link rel="canonical" href="%s">', esc_url($canonical));

        foreach ($alternates as $lang => $url) {
            $meta .= sprintf(
                '<link rel="alternate" hreflang="%s" href="%s">',
                esc_attr($lang),
                esc_url($url)
            );
        }

        return $meta;
    }

    /**
     * Generate robots meta tags
     */
    private function generateRobotsMeta(array $pageData): string
    {
        $robots = $pageData['robots'] ?? 'index, follow';
        $revisitAfter = $pageData['revisit_after'] ?? '7 days';

        return sprintf(
            '<meta name="robots" content="%s">
<meta name="revisit-after" content="%s">',
            esc_attr($robots),
            esc_attr($revisitAfter)
        );
    }

    /**
     * Optimize title for SEO
     */
    private function optimizeTitle(string $title, int $maxLength = 60): string
    {
        if (empty($title)) {
            return $this->siteName;
        }

        // Add site name if not present
        if (!str_contains($title, $this->siteName)) {
            $title .= ' | ' . $this->siteName;
        }

        // Truncate if too long
        if (strlen($title) > $maxLength) {
            $title = substr($title, 0, $maxLength - 3) . '...';
        }

        return $title;
    }

    /**
     * Optimize description for SEO
     */
    private function optimizeDescription(string $description, int $maxLength = 160): string
    {
        if (empty($description)) {
            return $this->config['default_description'] ?? 'Find your dream job with ' . $this->siteName;
        }

        // Clean HTML tags
        $description = strip_tags($description);
        
        // Truncate if too long
        if (strlen($description) > $maxLength) {
            $description = substr($description, 0, $maxLength - 3) . '...';
        }

        return $description;
    }

    /**
     * Generate keywords from page data
     */
    private function generateKeywords(array $pageData): array
    {
        $keywords = [];
        
        // Add explicit keywords
        if (!empty($pageData['keywords'])) {
            $keywords = array_merge($keywords, (array) $pageData['keywords']);
        }
        
        // Extract keywords from title and description
        $text = ($pageData['title'] ?? '') . ' ' . ($pageData['description'] ?? '');
        $extractedKeywords = $this->extractKeywords($text);
        $keywords = array_merge($keywords, $extractedKeywords);
        
        // Add default site keywords
        $keywords = array_merge($keywords, $this->config['default_keywords'] ?? []);
        
        return array_unique(array_filter($keywords));
    }

    /**
     * Extract keywords from text
     */
    private function extractKeywords(string $text): array
    {
        // Remove common stop words and extract meaningful terms
        $stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
        $words = str_word_count(strtolower($text), 1);
        $keywords = array_diff($words, $stopWords);
        
        return array_filter($keywords, fn($word) => strlen($word) > 3);
    }

    /**
     * Generate news-specific meta tags
     */
    private function generateNewsMeta(array $pageData): string
    {
        $publishDate = $pageData['published_date'] ?? date('c');
        $modifiedDate = $pageData['modified_date'] ?? $publishDate;
        $section = $pageData['section'] ?? 'Jobs';

        return sprintf(
            '<meta name="article:published_time" content="%s">
<meta name="article:modified_time" content="%s">
<meta name="article:section" content="%s">
<meta name="news_keywords" content="%s">',
            esc_attr($publishDate),
            esc_attr($modifiedDate),
            esc_attr($section),
            esc_attr(implode(', ', $pageData['keywords'] ?? []))
        );
    }

    /**
     * Get default image for social sharing
     */
    private function getDefaultImage(): string
    {
        return $this->config['default_image'] ?? $this->siteUrl . '/assets/images/og-default.jpg';
    }

    /**
     * Load SEO configuration
     */
    private function loadConfig(): void
    {
        $this->config = get_option('universal_seo_config', [
            'default_description' => 'Find your dream job with the best companies. Browse thousands of job opportunities.',
            'default_keywords' => ['jobs', 'careers', 'employment', 'hiring', 'work'],
            'twitter_handle' => '@yoursite',
            'google_site_verification' => '',
            'default_image' => ''
        ]);
    }
}
```

### Step 27.2: Create Favicon Management Service

#### manager/wp-content/themes/universal-theme/src/Services/FaviconService.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Theme\Services;

/**
 * Favicon Management Service
 * 
 * Handles favicon generation and optimization following Google guidelines
 * 
 * @package UniversalApp\Theme\Services
 * @since 1.0.0
 */
final class FaviconService
{
    private string $faviconPath;
    private string $faviconUrl;

    public function __construct()
    {
        $this->faviconPath = ABSPATH . '../assets/images/favicons/';
        $this->faviconUrl = get_option('universal_static_site_url', home_url()) . '/assets/images/favicons/';
    }

    /**
     * Generate all favicon formats
     */
    public function generateFavicons(string $sourceImage): bool
    {
        try {
            // Ensure favicon directory exists
            if (!is_dir($this->faviconPath)) {
                mkdir($this->faviconPath, 0755, true);
            }

            // Generate different favicon sizes
            $this->generateFaviconSizes($sourceImage);
            
            // Generate web app manifest
            $this->generateWebAppManifest();
            
            // Generate browserconfig.xml for Windows tiles
            $this->generateBrowserConfig();

            return true;
        } catch (\Exception $e) {
            error_log('Favicon generation failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate favicon HTML tags
     */
    public function getFaviconHTML(): string
    {
        return sprintf(
            '<!-- Favicons -->
<link rel="icon" type="image/x-icon" href="%sfavicon.ico">
<link rel="icon" type="image/png" sizes="16x16" href="%sfavicon-16x16.png">
<link rel="icon" type="image/png" sizes="32x32" href="%sfavicon-32x32.png">
<link rel="icon" type="image/png" sizes="48x48" href="%sfavicon-48x48.png">
<link rel="apple-touch-icon" sizes="180x180" href="%sapple-touch-icon.png">
<link rel="apple-touch-icon" sizes="152x152" href="%sapple-touch-icon-152x152.png">
<link rel="apple-touch-icon" sizes="144x144" href="%sapple-touch-icon-144x144.png">
<link rel="apple-touch-icon" sizes="120x120" href="%sapple-touch-icon-120x120.png">
<link rel="apple-touch-icon" sizes="114x114" href="%sapple-touch-icon-114x114.png">
<link rel="apple-touch-icon" sizes="76x76" href="%sapple-touch-icon-76x76.png">
<link rel="apple-touch-icon" sizes="72x72" href="%sapple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="60x60" href="%sapple-touch-icon-60x60.png">
<link rel="apple-touch-icon" sizes="57x57" href="%sapple-touch-icon-57x57.png">
<link rel="manifest" href="%ssite.webmanifest">
<link rel="mask-icon" href="%ssafari-pinned-tab.svg" color="#2563eb">
<meta name="msapplication-TileColor" content="#2563eb">
<meta name="msapplication-config" content="%sbrowserconfig.xml">
<meta name="theme-color" content="#ffffff">',
            $this->faviconUrl, $this->faviconUrl, $this->faviconUrl, $this->faviconUrl,
            $this->faviconUrl, $this->faviconUrl, $this->faviconUrl, $this->faviconUrl,
            $this->faviconUrl, $this->faviconUrl, $this->faviconUrl, $this->faviconUrl,
            $this->faviconUrl, $this->faviconUrl, $this->faviconUrl, $this->faviconUrl
        );
    }

    /**
     * Generate different favicon sizes
     */
    private function generateFaviconSizes(string $sourceImage): void
    {
        $sizes = [
            'favicon.ico' => 32,
            'favicon-16x16.png' => 16,
            'favicon-32x32.png' => 32,
            'favicon-48x48.png' => 48,
            'apple-touch-icon.png' => 180,
            'apple-touch-icon-152x152.png' => 152,
            'apple-touch-icon-144x144.png' => 144,
            'apple-touch-icon-120x120.png' => 120,
            'apple-touch-icon-114x114.png' => 114,
            'apple-touch-icon-76x76.png' => 76,
            'apple-touch-icon-72x72.png' => 72,
            'apple-touch-icon-60x60.png' => 60,
            'apple-touch-icon-57x57.png' => 57,
            'android-chrome-192x192.png' => 192,
            'android-chrome-512x512.png' => 512,
            'mstile-150x150.png' => 150
        ];

        foreach ($sizes as $filename => $size) {
            $this->resizeImage($sourceImage, $this->faviconPath . $filename, $size);
        }
    }

    /**
     * Generate web app manifest
     */
    private function generateWebAppManifest(): void
    {
        $manifest = [
            'name' => get_bloginfo('name'),
            'short_name' => get_bloginfo('name'),
            'description' => get_bloginfo('description'),
            'start_url' => '/',
            'display' => 'standalone',
            'theme_color' => '#ffffff',
            'background_color' => '#ffffff',
            'icons' => [
                [
                    'src' => $this->faviconUrl . 'android-chrome-192x192.png',
                    'sizes' => '192x192',
                    'type' => 'image/png'
                ],
                [
                    'src' => $this->faviconUrl . 'android-chrome-512x512.png',
                    'sizes' => '512x512',
                    'type' => 'image/png'
                ]
            ]
        ];

        file_put_contents(
            $this->faviconPath . 'site.webmanifest',
            json_encode($manifest, JSON_PRETTY_PRINT)
        );
    }

    /**
     * Generate browser config for Windows tiles
     */
    private function generateBrowserConfig(): void
    {
        $xml = sprintf(
            '<?xml version="1.0" encoding="utf-8"?>
<browserconfig>
    <msapplication>
        <tile>
            <square150x150logo src="%smstile-150x150.png"/>
            <TileColor>#2563eb</TileColor>
        </tile>
    </msapplication>
</browserconfig>',
            $this->faviconUrl
        );

        file_put_contents($this->faviconPath . 'browserconfig.xml', $xml);
    }

    /**
     * Resize image to specified dimensions
     */
    private function resizeImage(string $source, string $destination, int $size): void
    {
        // This would use GD or ImageMagick to resize images
        // Implementation depends on available image processing library
        
        if (extension_loaded('gd')) {
            $this->resizeWithGD($source, $destination, $size);
        } elseif (extension_loaded('imagick')) {
            $this->resizeWithImageMagick($source, $destination, $size);
        } else {
            throw new \Exception('No image processing library available');
        }
    }

    /**
     * Resize image using GD library
     */
    private function resizeWithGD(string $source, string $destination, int $size): void
    {
        $sourceImage = imagecreatefromstring(file_get_contents($source));
        $resizedImage = imagecreatetruecolor($size, $size);
        
        // Preserve transparency
        imagealphablending($resizedImage, false);
        imagesavealpha($resizedImage, true);
        
        imagecopyresampled(
            $resizedImage, $sourceImage,
            0, 0, 0, 0,
            $size, $size,
            imagesx($sourceImage), imagesy($sourceImage)
        );

        if (str_ends_with($destination, '.ico')) {
            // Convert to ICO format (simplified)
            imagepng($resizedImage, str_replace('.ico', '.png', $destination));
        } else {
            imagepng($resizedImage, $destination);
        }

        imagedestroy($sourceImage);
        imagedestroy($resizedImage);
    }

    /**
     * Resize image using ImageMagick
     */
    private function resizeWithImageMagick(string $source, string $destination, int $size): void
    {
        $imagick = new \Imagick($source);
        $imagick->resizeImage($size, $size, \Imagick::FILTER_LANCZOS, 1);
        $imagick->writeImage($destination);
        $imagick->destroy();
    }
}
```

### Step 27.3: Create Core Web Vitals Optimizer

#### src/js/modules/CoreWebVitalsOptimizer.js
```javascript
/**
 * Core Web Vitals Optimizer
 * 
 * Optimizes Core Web Vitals metrics following Google guidelines
 * 
 * @package UniversalApp
 * @since 1.0.0
 */
class CoreWebVitalsOptimizer {
    constructor() {
        this.metrics = {
            lcp: null,
            fid: null,
            cls: null,
            fcp: null,
            ttfb: null
        };
        
        this.init();
    }

    /**
     * Initialize Core Web Vitals monitoring and optimization
     */
    init() {
        // Measure and optimize Core Web Vitals
        this.measureLCP();
        this.measureFID();
        this.measureCLS();
        this.measureFCP();
        this.measureTTFB();
        
        // Implement optimizations
        this.optimizeLCP();
        this.optimizeFID();
        this.optimizeCLS();
        
        // Send metrics to analytics
        this.sendMetricsToAnalytics();
    }

    /**
     * Measure Largest Contentful Paint (LCP)
     */
    measureLCP() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.metrics.lcp = lastEntry.startTime;
                
                // Log if LCP is poor (> 2.5s)
                if (lastEntry.startTime > 2500) {
                    console.warn('Poor LCP detected:', lastEntry.startTime);
                }
            });
            
            observer.observe({ entryTypes: ['largest-contentful-paint'] });
        }
    }

    /**
     * Measure First Input Delay (FID)
     */
    measureFID() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach((entry) => {
                    this.metrics.fid = entry.processingStart - entry.startTime;
                    
                    // Log if FID is poor (> 100ms)
                    if (this.metrics.fid > 100) {
                        console.warn('Poor FID detected:', this.metrics.fid);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['first-input'] });
        }
    }

    /**
     * Measure Cumulative Layout Shift (CLS)
     */
    measureCLS() {
        if ('PerformanceObserver' in window) {
            let clsValue = 0;
            
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach((entry) => {
                    if (!entry.hadRecentInput) {
                        clsValue += entry.value;
                    }
                });
                
                this.metrics.cls = clsValue;
                
                // Log if CLS is poor (> 0.1)
                if (clsValue > 0.1) {
                    console.warn('Poor CLS detected:', clsValue);
                }
            });
            
            observer.observe({ entryTypes: ['layout-shift'] });
        }
    }

    /**
     * Measure First Contentful Paint (FCP)
     */
    measureFCP() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach((entry) => {
                    if (entry.name === 'first-contentful-paint') {
                        this.metrics.fcp = entry.startTime;
                        
                        // Log if FCP is poor (> 1.8s)
                        if (entry.startTime > 1800) {
                            console.warn('Poor FCP detected:', entry.startTime);
                        }
                    }
                });
            });
            
            observer.observe({ entryTypes: ['paint'] });
        }
    }

    /**
     * Measure Time to First Byte (TTFB)
     */
    measureTTFB() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach((entry) => {
                    if (entry.entryType === 'navigation') {
                        this.metrics.ttfb = entry.responseStart - entry.requestStart;
                        
                        // Log if TTFB is poor (> 800ms)
                        if (this.metrics.ttfb > 800) {
                            console.warn('Poor TTFB detected:', this.metrics.ttfb);
                        }
                    }
                });
            });
            
            observer.observe({ entryTypes: ['navigation'] });
        }
    }

    /**
     * Optimize Largest Contentful Paint
     */
    optimizeLCP() {
        // Preload critical resources
        this.preloadCriticalResources();
        
        // Optimize images
        this.optimizeImages();
        
        // Remove render-blocking resources
        this.removeRenderBlockingResources();
    }

    /**
     * Optimize First Input Delay
     */
    optimizeFID() {
        // Break up long tasks
        this.breakUpLongTasks();
        
        // Use web workers for heavy computations
        this.useWebWorkers();
        
        // Optimize third-party code
        this.optimizeThirdPartyCode();
    }

    /**
     * Optimize Cumulative Layout Shift
     */
    optimizeCLS() {
        // Set size attributes on images and videos
        this.setSizeAttributes();
        
        // Reserve space for ads
        this.reserveAdSpace();
        
        // Avoid inserting content above existing content
        this.avoidContentInsertion();
    }

    /**
     * Preload critical resources
     */
    preloadCriticalResources() {
        const criticalResources = [
            '/assets/css/critical.css',
            '/assets/fonts/primary-font.woff2',
            '/assets/images/hero-image.webp'
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            
            if (resource.includes('.css')) {
                link.as = 'style';
            } else if (resource.includes('.woff')) {
                link.as = 'font';
                link.type = 'font/woff2';
                link.crossOrigin = 'anonymous';
            } else if (resource.includes('.webp') || resource.includes('.jpg')) {
                link.as = 'image';
            }
            
            document.head.appendChild(link);
        });
    }

    /**
     * Optimize images for better LCP
     */
    optimizeImages() {
        const images = document.querySelectorAll('img');
        
        images.forEach(img => {
            // Add loading="lazy" for non-critical images
            if (!img.closest('.hero, .above-fold')) {
                img.loading = 'lazy';
            }
            
            // Add decoding="async" for better performance
            img.decoding = 'async';
            
            // Ensure proper sizing
            if (!img.width || !img.height) {
                console.warn('Image missing dimensions:', img.src);
            }
        });
    }

    /**
     * Break up long tasks to improve FID
     */
    breakUpLongTasks() {
        // Use scheduler.postTask if available, otherwise setTimeout
        const scheduleTask = (callback) => {
            if ('scheduler' in window && 'postTask' in scheduler) {
                scheduler.postTask(callback, { priority: 'user-blocking' });
            } else {
                setTimeout(callback, 0);
            }
        };

        // Example: Break up heavy initialization
        const heavyTasks = this.getHeavyTasks();
        heavyTasks.forEach((task, index) => {
            scheduleTask(() => {
                task();
            });
        });
    }

    /**
     * Set size attributes to prevent CLS
     */
    setSizeAttributes() {
        // Ensure all images have width and height
        const images = document.querySelectorAll('img:not([width]), img:not([height])');
        images.forEach(img => {
            console.warn('Image missing size attributes:', img.src);
        });

        // Ensure videos have dimensions
        const videos = document.querySelectorAll('video:not([width]), video:not([height])');
        videos.forEach(video => {
            console.warn('Video missing size attributes:', video.src);
        });
    }

    /**
     * Reserve space for ads to prevent CLS
     */
    reserveAdSpace() {
        const adContainers = document.querySelectorAll('.ad-container');
        adContainers.forEach(container => {
            if (!container.style.minHeight) {
                // Set minimum height based on ad type
                const adType = container.dataset.adType;
                const minHeight = this.getAdMinHeight(adType);
                container.style.minHeight = minHeight + 'px';
            }
        });
    }

    /**
     * Get minimum height for ad types
     */
    getAdMinHeight(adType) {
        const adSizes = {
            'banner': 90,
            'rectangle': 250,
            'skyscraper': 600,
            'mobile-banner': 50
        };
        
        return adSizes[adType] || 250;
    }

    /**
     * Send metrics to analytics
     */
    sendMetricsToAnalytics() {
        // Wait for page load to complete
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (window.gtag) {
                    // Send Core Web Vitals to Google Analytics
                    Object.entries(this.metrics).forEach(([metric, value]) => {
                        if (value !== null) {
                            gtag('event', 'core_web_vitals', {
                                event_category: 'Performance',
                                event_label: metric.toUpperCase(),
                                value: Math.round(value),
                                custom_map: { metric: metric }
                            });
                        }
                    });
                }
            }, 1000);
        });
    }

    /**
     * Get heavy tasks that should be broken up
     */
    getHeavyTasks() {
        return [
            () => this.initializeComponents(),
            () => this.loadAnalytics(),
            () => this.setupEventListeners()
        ];
    }

    /**
     * Placeholder methods for heavy tasks
     */
    initializeComponents() {
        // Component initialization logic
    }

    loadAnalytics() {
        // Analytics loading logic
    }

    setupEventListeners() {
        // Event listener setup logic
    }

    removeRenderBlockingResources() {
        // Implementation for removing render-blocking resources
    }

    useWebWorkers() {
        // Implementation for using web workers
    }

    optimizeThirdPartyCode() {
        // Implementation for optimizing third-party code
    }

    avoidContentInsertion() {
        // Implementation for avoiding content insertion above existing content
    }
}

// Initialize Core Web Vitals optimizer
const coreWebVitalsOptimizer = new CoreWebVitalsOptimizer();

export default CoreWebVitalsOptimizer;
```

## Testing Checklist

### SEO Meta Tags
- [ ] Title tags are optimized (under 60 characters)
- [ ] Meta descriptions are compelling (under 160 characters)
- [ ] Open Graph tags are complete and accurate
- [ ] Twitter Card tags are properly formatted
- [ ] Canonical URLs are set correctly
- [ ] Robots meta tags are appropriate

### Favicon Implementation
- [ ] All favicon sizes are generated
- [ ] Web app manifest is created
- [ ] Browser config XML is generated
- [ ] Favicons display correctly across devices
- [ ] Apple touch icons work on iOS
- [ ] Windows tiles display properly

### Core Web Vitals
- [ ] LCP is under 2.5 seconds
- [ ] FID is under 100 milliseconds
- [ ] CLS is under 0.1
- [ ] FCP is under 1.8 seconds
- [ ] TTFB is under 800 milliseconds
- [ ] Metrics are tracked in analytics

### Google Search Console
- [ ] Site is verified in Search Console
- [ ] Sitemaps are submitted
- [ ] Rich results are validated
- [ ] Mobile usability is confirmed
- [ ] Core Web Vitals report shows good scores

## Success Criteria
1. ✅ Advanced SEO meta tags implemented
2. ✅ Favicon system operational
3. ✅ Core Web Vitals optimized
4. ✅ Google Search Console integration
5. ✅ Performance metrics tracked
6. ✅ Mobile optimization confirmed
7. ✅ All files under 150 lines
8. ✅ Live testing passes

## Next Stage
Once Stage 27 is complete and all tests pass, proceed to **Stage 28: Advanced Schema Markup** where we'll implement comprehensive structured data following Google's latest guidelines for job postings, organizations, and rich results.
