# WordPress Universal Theme File Structure

## Theme Architecture Overview

### Modern PHP 8+ Features Used
- **Namespaces**: `UniversalApp\Theme\*`
- **Type Declarations**: Strict typing for all methods
- **Traits**: Reusable functionality across classes
- **Dependency Injection**: PSR-11 container implementation
- **Attributes**: PHP 8+ annotations for metadata
- **Enums**: Type-safe constants
- **Anonymous Classes**: For one-time use implementations

## Root Theme Structure
```
universal-theme/
├── style.css                          (Theme header file)
├── index.php                          (Main template fallback)
├── functions.php                      (Theme initialization)
├── composer.json                      (Dependencies & autoloading)
├── package.json                       (Frontend dependencies)
├── webpack.config.js                  (Asset bundling)
├── .env.example                       (Environment variables)
├── phpunit.xml                        (Testing configuration)
└── README.md                          (Documentation)
```

## Core Directory Structure
```
src/
├── Core/                              (Core theme functionality)
│   ├── Application.php                (Main application class)
│   ├── ServiceProvider.php           (DI container setup)
│   ├── Bootstrap.php                  (Theme initialization)
│   └── Container.php                  (PSR-11 DI container)
├── Config/                            (Configuration management)
│   ├── ThemeConfig.php               (Theme configuration)
│   ├── PlatformConfig.php            (Platform-specific configs)
│   └── AssetConfig.php               (Asset management config)
├── Services/                          (Business logic services)
│   ├── TemplateService.php           (Template rendering)
│   ├── AssetService.php              (Asset management)
│   ├── ConfigService.php             (Configuration handling)
│   └── StaticGeneratorService.php    (Static site generation)
├── Controllers/                       (Request handling)
│   ├── BaseController.php            (Abstract base controller)
│   ├── HomeController.php            (Homepage logic)
│   ├── JobController.php             (Job-related pages)
│   └── CompanyController.php         (Company pages)
├── Models/                            (Data models)
│   ├── BaseModel.php                 (Abstract base model)
│   ├── Job.php                       (Job data model)
│   ├── Company.php                   (Company data model)
│   └── Category.php                  (Category data model)
├── Views/                             (Template files)
│   ├── layouts/                      (Layout templates)
│   ├── components/                   (Reusable components)
│   ├── pages/                        (Page templates)
│   └── partials/                     (Template partials)
├── Traits/                            (Reusable functionality)
│   ├── Configurable.php              (Configuration trait)
│   ├── Renderable.php                (Rendering trait)
│   └── Cacheable.php                 (Caching trait)
├── Interfaces/                        (Contracts)
│   ├── ConfigurableInterface.php     (Configuration contract)
│   ├── RenderableInterface.php       (Rendering contract)
│   └── CacheableInterface.php        (Caching contract)
├── Enums/                             (Type-safe constants)
│   ├── PlatformType.php              (Platform types)
│   ├── ContentType.php               (Content types)
│   └── AssetType.php                 (Asset types)
└── Exceptions/                        (Custom exceptions)
    ├── ThemeException.php             (Base theme exception)
    ├── ConfigException.php            (Configuration errors)
    └── TemplateException.php          (Template errors)
```

## Assets Directory Structure
```
assets/
├── css/                               (Stylesheets)
│   ├── src/                          (Source CSS files)
│   │   ├── base/                     (Base styles)
│   │   │   ├── reset.css             (CSS reset)
│   │   │   ├── typography.css        (Typography system)
│   │   │   └── variables.css         (CSS custom properties)
│   │   ├── components/               (Component styles)
│   │   │   ├── header.css            (Header component)
│   │   │   ├── footer.css            (Footer component)
│   │   │   ├── navigation.css        (Navigation styles)
│   │   │   └── cards.css             (Card components)
│   │   ├── layouts/                  (Layout styles)
│   │   │   ├── grid.css              (CSS Grid layouts)
│   │   │   ├── flexbox.css           (Flexbox utilities)
│   │   │   └── container.css         (Container queries)
│   │   ├── pages/                    (Page-specific styles)
│   │   │   ├── home.css              (Homepage styles)
│   │   │   ├── jobs.css              (Job pages)
│   │   │   └── companies.css         (Company pages)
│   │   └── utilities/                (Utility classes)
│   │       ├── spacing.css           (Margin/padding utilities)
│   │       ├── colors.css            (Color utilities)
│   │       └── responsive.css        (Responsive utilities)
│   └── dist/                         (Compiled CSS)
├── js/                                (JavaScript files)
│   ├── src/                          (Source JS files)
│   │   ├── core/                     (Core functionality)
│   │   │   ├── Application.js        (Main app class)
│   │   │   ├── EventBus.js           (Event system)
│   │   │   └── ServiceWorker.js      (PWA functionality)
│   │   ├── components/               (JS components)
│   │   │   ├── SearchWidget.js       (Search functionality)
│   │   │   ├── FilterWidget.js       (Filtering)
│   │   │   └── AuthWidget.js         (Authentication)
│   │   ├── services/                 (Service classes)
│   │   │   ├── ApiService.js         (API communication)
│   │   │   ├── CacheService.js       (Caching)
│   │   │   └── AnalyticsService.js   (Analytics)
│   │   ├── utils/                    (Utility functions)
│   │   │   ├── debounce.js           (Debouncing)
│   │   │   ├── throttle.js           (Throttling)
│   │   │   └── memoize.js            (Memoization)
│   │   └── workers/                  (Web Workers)
│   │       ├── search-worker.js      (Search processing)
│   │       └── cache-worker.js       (Cache management)
│   └── dist/                         (Compiled JS)
├── images/                            (Image assets)
│   ├── src/                          (Source images)
│   └── dist/                         (Optimized images)
└── fonts/                             (Font files)
    ├── primary/                      (Primary font family)
    └── secondary/                    (Secondary font family)
```

## Template Structure
```
templates/
├── layouts/                           (Layout templates)
│   ├── base.php                      (Master layout)
│   ├── single-column.php             (Single column layout)
│   ├── two-column.php                (Two column layout)
│   └── three-column.php              (Three column layout)
├── components/                        (Reusable components)
│   ├── header.php                    (Site header)
│   ├── footer.php                    (Site footer)
│   ├── navigation.php                (Main navigation)
│   ├── sidebar.php                   (Sidebar content)
│   ├── breadcrumbs.php               (Breadcrumb navigation)
│   └── pagination.php                (Pagination component)
├── pages/                             (Page templates)
│   ├── home.php                      (Homepage template)
│   ├── jobs/                         (Job-related templates)
│   │   ├── archive.php               (Job listing page)
│   │   ├── single.php                (Single job page)
│   │   └── search.php                (Job search results)
│   ├── companies/                    (Company templates)
│   │   ├── archive.php               (Company listing)
│   │   └── single.php                (Company profile)
│   └── categories/                   (Category templates)
│       ├── archive.php               (Category listing)
│       └── single.php                (Category page)
├── partials/                          (Template partials)
│   ├── job-card.php                  (Job card component)
│   ├── company-card.php              (Company card)
│   ├── meta-tags.php                 (SEO meta tags)
│   └── schema-markup.php             (Structured data)
└── react/                             (React mount points)
    ├── auth-widget.php               (Auth widget mount)
    ├── search-widget.php             (Search widget mount)
    └── member-dashboard.php          (Dashboard mount)
```

## Configuration Structure
```
config/
├── theme.php                         (Main theme config)
├── platforms/                        (Platform configurations)
│   ├── job-portal.php               (Job portal config)
│   ├── comparison-site.php          (Comparison site config)
│   └── forex-platform.php          (Forex platform config)
├── content-types/                    (Content type definitions)
│   ├── jobs.php                     (Job content type)
│   ├── companies.php               (Company content type)
│   └── categories.php              (Category content type)
├── assets.php                       (Asset configuration)
├── seo.php                          (SEO settings)
└── performance.php                  (Performance settings)
```

## Testing Structure
```
tests/
├── Unit/                             (Unit tests)
│   ├── Core/                        (Core functionality tests)
│   ├── Services/                    (Service tests)
│   ├── Models/                      (Model tests)
│   └── Controllers/                 (Controller tests)
├── Integration/                      (Integration tests)
│   ├── TemplateTest.php             (Template rendering tests)
│   ├── AssetTest.php                (Asset loading tests)
│   └── ConfigTest.php               (Configuration tests)
├── Feature/                          (Feature tests)
│   ├── JobPageTest.php              (Job page functionality)
│   ├── CompanyPageTest.php          (Company page functionality)
│   └── SearchTest.php               (Search functionality)
└── fixtures/                        (Test data)
    ├── jobs.json                    (Sample job data)
    ├── companies.json               (Sample company data)
    └── config.json                  (Test configuration)
```

## Build Tools Structure
```
build/
├── webpack/                          (Webpack configurations)
│   ├── webpack.common.js            (Common config)
│   ├── webpack.dev.js               (Development config)
│   └── webpack.prod.js              (Production config)
├── postcss/                          (PostCSS configurations)
│   ├── postcss.config.js            (Main PostCSS config)
│   └── plugins/                     (Custom PostCSS plugins)
├── babel/                            (Babel configurations)
│   ├── babel.config.js              (Main Babel config)
│   └── presets/                     (Custom Babel presets)
└── scripts/                          (Build scripts)
    ├── build.js                     (Main build script)
    ├── dev.js                       (Development script)
    └── optimize.js                  (Optimization script)
```

## Key Features

### Modern PHP Architecture
- **PSR-4 Autoloading**: Composer-based class loading
- **PSR-11 Container**: Dependency injection container
- **PSR-3 Logging**: Structured logging system
- **Type Safety**: Strict type declarations throughout
- **Error Handling**: Comprehensive exception handling

### Advanced CSS Features
- **CSS Custom Properties**: Dynamic theming system
- **CSS Grid & Subgrid**: Advanced layout capabilities
- **Container Queries**: Component-responsive design
- **CSS Layers**: Cascade control and organization
- **Logical Properties**: Internationalization support

### Modern JavaScript Architecture
- **ES6+ Modules**: Modern module system
- **Web Workers**: Background processing
- **Service Workers**: PWA capabilities
- **Proxy/Reflect APIs**: Advanced object manipulation
- **Performance Optimization**: Debouncing, throttling, memoization

### File Organization Rules
- Maximum 150 lines per file
- Single responsibility principle
- Clear naming conventions
- Proper namespace organization
- Comprehensive documentation
