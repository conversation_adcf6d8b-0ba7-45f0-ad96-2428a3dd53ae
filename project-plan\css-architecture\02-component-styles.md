# Component-Based CSS Architecture

## Component CSS Strategy

### Component Organization
Each UI component has its own CSS file with scoped styles, following BEM methodology for naming conventions.

```
/components/
├── header.css              (site header)
├── footer.css              (site footer)
├── sidebar.css             (sidebar content)
├── navigation.css          (main navigation)
├── breadcrumbs.css         (navigation breadcrumbs)
├── pagination.css          (page navigation)
├── search-form.css         (search interface)
├── job-card.css            (job listing card)
├── company-card.css        (company listing card)
├── forms.css               (form elements)
├── buttons.css             (button components)
├── modals.css              (modal dialogs)
└── notifications.css       (alert/notification components)
```

## Header Component Styles

### Header Structure and Styles
```css
/* components/header.css */
.header {
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(8px);
}

.header__container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem var(--container-padding);
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.header__logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--theme-primary);
  text-decoration: none;
}

.header__logo-image {
  width: 32px;
  height: 32px;
}

.header__nav {
  display: none;
}

.header__actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header__menu-toggle {
  display: block;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
}

/* Desktop styles */
@media (min-width: 768px) {
  .header__nav {
    display: flex;
    align-items: center;
    gap: 2rem;
  }
  
  .header__menu-toggle {
    display: none;
  }
}
```

### Navigation Component
```css
/* components/navigation.css */
.nav {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.nav__list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 1.5rem;
}

.nav__item {
  position: relative;
}

.nav__link {
  display: block;
  padding: 0.5rem 0;
  color: var(--color-text);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.nav__link:hover,
.nav__link--active {
  color: var(--theme-primary);
}

.nav__link--active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--theme-primary);
}

/* Dropdown navigation */
.nav__dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  min-width: 200px;
  z-index: 10;
}

.nav__item:hover .nav__dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
```

## Card Component Styles

### Job Card Component
```css
/* components/job-card.css */
.job-card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.2s ease;
  container-type: inline-size;
}

.job-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.job-card--featured {
  border-left: 4px solid var(--theme-accent);
  background: linear-gradient(135deg, var(--color-surface) 0%, var(--job-featured) 100%);
}

.job-card__header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.job-card__title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-text);
  margin: 0 0 0.25rem 0;
  line-height: var(--leading-tight);
}

.job-card__title-link {
  color: inherit;
  text-decoration: none;
  transition: color 0.2s ease;
}

.job-card__title-link:hover {
  color: var(--theme-primary);
}

.job-card__company {
  font-size: var(--text-base);
  color: var(--color-text-muted);
  margin-bottom: 0.5rem;
}

.job-card__meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.job-card__meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: var(--text-sm);
  color: var(--color-text-muted);
}

.job-card__meta-icon {
  width: 16px;
  height: 16px;
  opacity: 0.7;
}

.job-card__description {
  font-size: var(--text-sm);
  color: var(--color-text-muted);
  line-height: var(--leading-relaxed);
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.job-card__footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid var(--color-border);
}

.job-card__salary {
  font-weight: 600;
  color: var(--salary-high);
}

.job-card__actions {
  display: flex;
  gap: 0.5rem;
}

/* Container query responsive design */
@container (min-width: 400px) {
  .job-card {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1rem;
  }
  
  .job-card__content {
    grid-column: 1;
  }
  
  .job-card__actions {
    grid-column: 2;
    flex-direction: column;
    align-self: start;
  }
}
```

### Company Card Component
```css
/* components/company-card.css */
.company-card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.75rem;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
}

.company-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.company-card__logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  margin: 0 auto 1rem auto;
  border: 2px solid var(--color-border);
}

.company-card__name {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--color-text);
}

.company-card__industry {
  font-size: var(--text-sm);
  color: var(--color-text-muted);
  margin-bottom: 1rem;
}

.company-card__stats {
  display: flex;
  justify-content: space-around;
  padding-top: 1rem;
  border-top: 1px solid var(--color-border);
}

.company-card__stat {
  text-align: center;
}

.company-card__stat-number {
  display: block;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--theme-primary);
}

.company-card__stat-label {
  font-size: var(--text-xs);
  color: var(--color-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
```

## Form Component Styles

### Form Elements
```css
/* components/forms.css */
.form {
  max-width: 500px;
}

.form__group {
  margin-bottom: 1.5rem;
}

.form__label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--color-text);
}

.form__label--required::after {
  content: ' *';
  color: var(--color-error);
}

.form__input,
.form__textarea,
.form__select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  font-size: var(--text-base);
  transition: all 0.2s ease;
  background: var(--color-surface);
}

.form__input:focus,
.form__textarea:focus,
.form__select:focus {
  outline: none;
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form__input--error,
.form__textarea--error,
.form__select--error {
  border-color: var(--color-error);
}

.form__error {
  display: block;
  font-size: var(--text-sm);
  color: var(--color-error);
  margin-top: 0.25rem;
}

.form__help {
  display: block;
  font-size: var(--text-sm);
  color: var(--color-text-muted);
  margin-top: 0.25rem;
}

.form__checkbox,
.form__radio {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.form__checkbox-input,
.form__radio-input {
  width: auto;
  margin: 0;
}
```

### Button Components
```css
/* components/buttons.css */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: var(--text-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn--primary {
  background: var(--theme-primary);
  color: white;
}

.btn--primary:hover:not(:disabled) {
  background: var(--color-primary-700);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.btn--secondary {
  background: var(--color-gray-100);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.btn--secondary:hover:not(:disabled) {
  background: var(--color-gray-200);
}

.btn--outline {
  background: transparent;
  color: var(--theme-primary);
  border: 1px solid var(--theme-primary);
}

.btn--outline:hover:not(:disabled) {
  background: var(--theme-primary);
  color: white;
}

.btn--small {
  padding: 0.5rem 1rem;
  font-size: var(--text-sm);
}

.btn--large {
  padding: 1rem 2rem;
  font-size: var(--text-lg);
}

.btn--full {
  width: 100%;
}

.btn__icon {
  width: 1em;
  height: 1em;
}
```

## Utility Component Styles

### Modal Component
```css
/* components/modals.css */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal--open {
  opacity: 1;
  visibility: visible;
}

.modal__content {
  background: var(--color-surface);
  border-radius: 0.75rem;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal--open .modal__content {
  transform: scale(1);
}

.modal__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-border);
}

.modal__title {
  font-size: var(--text-xl);
  font-weight: 600;
  margin: 0;
}

.modal__close {
  background: none;
  border: none;
  font-size: var(--text-xl);
  cursor: pointer;
  padding: 0.25rem;
  color: var(--color-text-muted);
}
```

### Notification Component
```css
/* components/notifications.css */
.notification {
  position: fixed;
  top: 1rem;
  right: 1rem;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  padding: 1rem 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  max-width: 400px;
}

.notification--show {
  transform: translateX(0);
}

.notification--success {
  border-left: 4px solid var(--color-success);
}

.notification--error {
  border-left: 4px solid var(--color-error);
}

.notification--warning {
  border-left: 4px solid var(--color-warning);
}

.notification--info {
  border-left: 4px solid var(--color-info);
}

.notification__content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.notification__icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.notification__message {
  flex: 1;
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
}

.notification__close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  color: var(--color-text-muted);
  margin-left: auto;
}
```
