# Build Process and Optimization

## Build Pipeline Overview

### Multi-Stage Build Process
```
Content Fetch → Data Processing → Template Generation → Asset Compilation → Optimization → Deployment
      ↓               ↓                ↓                    ↓                ↓              ↓
WordPress API → JSON Processing → HTML Generation → CSS/JS Bundling → Compression → CDN Upload
```

## Build Configuration

### Environment-Specific Builds
```javascript
// build.config.js
const buildConfigs = {
  development: {
    minification: false,
    sourceMaps: true,
    optimization: 'none',
    bundleAnalysis: true,
    hotReload: true,
    buildTime: 'fast',
    assetOptimization: false
  },
  
  staging: {
    minification: true,
    sourceMaps: true,
    optimization: 'basic',
    bundleAnalysis: true,
    hotReload: false,
    buildTime: 'medium',
    assetOptimization: true
  },
  
  production: {
    minification: true,
    sourceMaps: false,
    optimization: 'aggressive',
    bundleAnalysis: false,
    hotReload: false,
    buildTime: 'thorough',
    assetOptimization: true,
    compressionLevel: 9
  }
};

export default buildConfigs;
```

### Build Scripts
```json
{
  "scripts": {
    "build": "npm run clean && npm run fetch-content && npm run generate-pages && npm run compile-assets && npm run optimize",
    "build:dev": "NODE_ENV=development npm run build",
    "build:staging": "NODE_ENV=staging npm run build",
    "build:prod": "NODE_ENV=production npm run build",
    
    "clean": "rimraf dist/ && rimraf .cache/",
    "fetch-content": "node scripts/fetch-content.js",
    "generate-pages": "node scripts/generate-pages.js",
    "compile-assets": "webpack --config webpack.config.js",
    "optimize": "node scripts/optimize-assets.js",
    
    "build:incremental": "node scripts/incremental-build.js",
    "build:watch": "nodemon --watch src/ --watch content/ --exec 'npm run build:dev'",
    "build:analyze": "npm run build:prod && webpack-bundle-analyzer dist/assets/js/*.js",
    
    "serve": "http-server dist/ -p 3000 -c-1",
    "preview": "npm run build:prod && npm run serve"
  }
}
```

## Content Processing Pipeline

### WordPress Content Fetching
```javascript
// scripts/fetch-content.js
import axios from 'axios';
import fs from 'fs/promises';
import path from 'path';

class ContentFetcher {
  constructor(config) {
    this.baseURL = config.wordpress.apiUrl;
    this.cacheDir = config.build.cacheDir;
    this.endpoints = config.wordpress.endpoints;
  }

  async fetchAllContent() {
    console.log('🔄 Fetching content from WordPress...');
    
    const startTime = performance.now();
    const results = {};

    for (const [key, endpoint] of Object.entries(this.endpoints)) {
      console.log(`  📥 Fetching ${key}...`);
      results[key] = await this.fetchPaginatedContent(endpoint);
      console.log(`  ✅ Fetched ${results[key].length} ${key}`);
    }

    // Cache results
    await this.cacheContent(results);
    
    const duration = performance.now() - startTime;
    console.log(`✅ Content fetch completed in ${(duration / 1000).toFixed(2)}s`);
    
    return results;
  }

  async fetchPaginatedContent(endpoint) {
    const items = [];
    let page = 1;
    let hasMore = true;

    while (hasMore) {
      try {
        const response = await axios.get(`${this.baseURL}${endpoint}`, {
          params: {
            page,
            per_page: 100,
            _embed: true
          },
          timeout: 30000
        });

        items.push(...response.data);
        
        // Check if there are more pages
        const totalPages = parseInt(response.headers['x-wp-totalpages']);
        hasMore = page < totalPages;
        page++;
        
      } catch (error) {
        console.error(`Error fetching ${endpoint} page ${page}:`, error.message);
        hasMore = false;
      }
    }

    return items;
  }

  async cacheContent(content) {
    await fs.mkdir(this.cacheDir, { recursive: true });
    
    for (const [key, data] of Object.entries(content)) {
      const filePath = path.join(this.cacheDir, `${key}.json`);
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    }
  }

  async loadCachedContent() {
    const content = {};
    
    try {
      const files = await fs.readdir(this.cacheDir);
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          const key = file.replace('.json', '');
          const filePath = path.join(this.cacheDir, file);
          const data = await fs.readFile(filePath, 'utf-8');
          content[key] = JSON.parse(data);
        }
      }
    } catch (error) {
      console.warn('No cached content found, will fetch fresh data');
    }
    
    return content;
  }
}

export default ContentFetcher;
```

### Page Generation System
```javascript
// scripts/generate-pages.js
import Handlebars from 'handlebars';
import fs from 'fs/promises';
import path from 'path';
import { slugify } from '../src/utils/helpers.js';

class PageGenerator {
  constructor(config) {
    this.config = config;
    this.templatesDir = config.build.templatesDir;
    this.outputDir = config.build.outputDir;
    this.templates = new Map();
  }

  async init() {
    console.log('🔄 Initializing page generator...');
    
    // Load and compile templates
    await this.loadTemplates();
    
    // Register Handlebars helpers
    this.registerHelpers();
    
    console.log('✅ Page generator initialized');
  }

  async loadTemplates() {
    const templateFiles = await this.getTemplateFiles();
    
    for (const file of templateFiles) {
      const templateName = path.basename(file, '.hbs');
      const templateContent = await fs.readFile(file, 'utf-8');
      const compiledTemplate = Handlebars.compile(templateContent);
      
      this.templates.set(templateName, compiledTemplate);
    }
  }

  registerHelpers() {
    // Date formatting helper
    Handlebars.registerHelper('formatDate', (date, format) => {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    });

    // Truncate text helper
    Handlebars.registerHelper('truncate', (text, length) => {
      if (text.length <= length) return text;
      return text.substring(0, length) + '...';
    });

    // Conditional helper
    Handlebars.registerHelper('ifEquals', function(arg1, arg2, options) {
      return (arg1 == arg2) ? options.fn(this) : options.inverse(this);
    });

    // JSON helper
    Handlebars.registerHelper('json', (context) => {
      return JSON.stringify(context);
    });
  }

  async generateAllPages(content) {
    console.log('🔄 Generating static pages...');
    
    const startTime = performance.now();
    let pageCount = 0;

    // Generate homepage
    await this.generateHomepage(content);
    pageCount++;

    // Generate job pages
    if (content.jobs) {
      pageCount += await this.generateJobPages(content.jobs);
    }

    // Generate company pages
    if (content.companies) {
      pageCount += await this.generateCompanyPages(content.companies);
    }

    // Generate category pages
    if (content.categories) {
      pageCount += await this.generateCategoryPages(content.categories, content.jobs);
    }

    // Generate archive pages
    pageCount += await this.generateArchivePages(content);

    const duration = performance.now() - startTime;
    console.log(`✅ Generated ${pageCount} pages in ${(duration / 1000).toFixed(2)}s`);
  }

  async generateJobPages(jobs) {
    const template = this.templates.get('job-single');
    let count = 0;

    for (const job of jobs) {
      const slug = this.generateJobSlug(job);
      const filePath = path.join(this.outputDir, 'jobs', `${slug}.html`);
      
      const pageData = {
        job,
        pageTitle: `${job.title} at ${job.company} | JobHub`,
        pageDescription: job.excerpt,
        canonicalUrl: `${this.config.site.baseUrl}/jobs/${slug}.html`,
        schemaMarkup: this.generateJobSchema(job)
      };

      const html = template(pageData);
      
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      await fs.writeFile(filePath, html);
      
      count++;
    }

    return count;
  }

  generateJobSlug(job) {
    const title = slugify(job.title);
    const company = slugify(job.company);
    const location = slugify(job.location);
    
    return `${title}-${company}-${location}`;
  }

  generateJobSchema(job) {
    return JSON.stringify({
      "@context": "https://schema.org/",
      "@type": "JobPosting",
      "title": job.title,
      "description": job.description,
      "hiringOrganization": {
        "@type": "Organization",
        "name": job.company
      },
      "jobLocation": {
        "@type": "Place",
        "address": job.location
      },
      "datePosted": job.date,
      "employmentType": job.type,
      "baseSalary": job.salary ? {
        "@type": "MonetaryAmount",
        "currency": "USD",
        "value": job.salary
      } : undefined
    });
  }
}

export default PageGenerator;
```

## Asset Compilation

### Webpack Configuration - Single Bundle Strategy
```javascript
// webpack.config.js
import path from 'path';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import CssMinimizerPlugin from 'css-minimizer-webpack-plugin';
import TerserPlugin from 'terser-webpack-plugin';
import PurgeCSSPlugin from 'purgecss-webpack-plugin';
import CompressionPlugin from 'compression-webpack-plugin';

const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

export default {
  mode: process.env.NODE_ENV || 'development',

  entry: {
    main: './src/js/main.js',
    critical: './src/css/critical.css'
  },

  output: {
    path: path.resolve('dist/assets'),
    filename: isProduction ? 'js/[name].min.js' : 'js/[name].js',
    clean: true
  },
  
  module: {
    rules: [
      {
        test: /\.jsx?$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', { targets: 'defaults' }],
              ['@babel/preset-react', { runtime: 'automatic' }]
            ]
          }
        }
      },
      {
        test: /\.css$/,
        use: [
          isDevelopment ? 'style-loader' : MiniCssExtractPlugin.loader,
          'css-loader',
          'postcss-loader'
        ]
      },
      {
        test: /\.(png|jpg|jpeg|gif|svg|webp)$/,
        type: 'asset/resource',
        generator: {
          filename: 'images/[name].[hash][ext]'
        }
      },
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/,
        type: 'asset/resource',
        generator: {
          filename: 'fonts/[name].[hash][ext]'
        }
      }
    ]
  },
  
  plugins: [
    new MiniCssExtractPlugin({
      filename: isProduction ? 'css/[name].[contenthash].css' : 'css/[name].css',
      chunkFilename: isProduction ? 'css/[name].[contenthash].chunk.css' : 'css/[name].chunk.css'
    }),
    
    ...(process.env.ANALYZE ? [new BundleAnalyzerPlugin()] : [])
  ],
  
  optimization: {
    minimize: isProduction,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: isProduction
          }
        }
      }),
      new CssMinimizerPlugin()
    ],
    
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true
        }
      }
    }
  },
  
  devtool: isDevelopment ? 'eval-source-map' : false,
  
  stats: {
    colors: true,
    modules: false,
    children: false,
    chunks: false,
    chunkModules: false
  }
};
```

## Build Optimization

### Image Optimization
```javascript
// scripts/optimize-images.js
import imagemin from 'imagemin';
import imageminWebp from 'imagemin-webp';
import imageminAvif from 'imagemin-avif';
import imageminMozjpeg from 'imagemin-mozjpeg';
import imageminPngquant from 'imagemin-pngquant';
import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';

class ImageOptimizer {
  constructor(config) {
    this.inputDir = config.images.inputDir;
    this.outputDir = config.images.outputDir;
    this.formats = config.images.formats;
    this.sizes = config.images.responsiveSizes;
  }

  async optimizeAllImages() {
    console.log('🔄 Optimizing images...');
    
    const startTime = performance.now();
    const imageFiles = await this.getImageFiles();
    
    for (const file of imageFiles) {
      await this.processImage(file);
    }
    
    const duration = performance.now() - startTime;
    console.log(`✅ Optimized ${imageFiles.length} images in ${(duration / 1000).toFixed(2)}s`);
  }

  async processImage(inputPath) {
    const filename = path.basename(inputPath, path.extname(inputPath));
    const relativePath = path.relative(this.inputDir, path.dirname(inputPath));
    const outputDir = path.join(this.outputDir, relativePath);
    
    await fs.mkdir(outputDir, { recursive: true });

    // Generate responsive sizes
    for (const size of this.sizes) {
      await this.generateResponsiveImage(inputPath, outputDir, filename, size);
    }

    // Generate modern formats
    await this.generateModernFormats(inputPath, outputDir, filename);
  }

  async generateResponsiveImage(inputPath, outputDir, filename, size) {
    const outputPath = path.join(outputDir, `${filename}-${size.width}w.jpg`);
    
    await sharp(inputPath)
      .resize(size.width, null, { withoutEnlargement: true })
      .jpeg({ quality: 85, progressive: true })
      .toFile(outputPath);
  }

  async generateModernFormats(inputPath, outputDir, filename) {
    // WebP format
    await imagemin([inputPath], {
      destination: outputDir,
      plugins: [
        imageminWebp({
          quality: 85,
          method: 6
        })
      ]
    });

    // AVIF format (for modern browsers)
    await imagemin([inputPath], {
      destination: outputDir,
      plugins: [
        imageminAvif({
          quality: 75,
          speed: 2
        })
      ]
    });
  }
}

export default ImageOptimizer;
```

### Critical CSS Extraction
```javascript
// scripts/extract-critical-css.js
import { generate } from 'critical';
import fs from 'fs/promises';
import path from 'path';

class CriticalCSSExtractor {
  constructor(config) {
    this.config = config;
    this.pages = config.criticalCSS.pages;
    this.outputDir = config.build.outputDir;
  }

  async extractCriticalCSS() {
    console.log('🔄 Extracting critical CSS...');
    
    for (const page of this.pages) {
      await this.extractForPage(page);
    }
    
    console.log('✅ Critical CSS extraction completed');
  }

  async extractForPage(page) {
    const htmlPath = path.join(this.outputDir, page.path);
    const cssPath = path.join(this.outputDir, 'assets/css/critical.css');
    
    try {
      const { css } = await generate({
        base: this.outputDir,
        src: page.path,
        css: ['assets/css/site.css'],
        dimensions: [
          { width: 320, height: 568 },  // Mobile
          { width: 768, height: 1024 }, // Tablet
          { width: 1200, height: 900 }  // Desktop
        ],
        penthouse: {
          blockJSRequests: false,
          timeout: 30000
        }
      });

      // Inline critical CSS
      const html = await fs.readFile(htmlPath, 'utf-8');
      const updatedHTML = html.replace(
        '</head>',
        `<style>${css}</style></head>`
      );
      
      await fs.writeFile(htmlPath, updatedHTML);
      
    } catch (error) {
      console.error(`Failed to extract critical CSS for ${page.path}:`, error);
    }
  }
}

export default CriticalCSSExtractor;
```

## Build Performance Monitoring

### Build Metrics Collection
```javascript
// scripts/build-metrics.js
class BuildMetrics {
  constructor() {
    this.metrics = {
      startTime: Date.now(),
      stages: {},
      assets: {},
      performance: {}
    };
  }

  startStage(stageName) {
    this.metrics.stages[stageName] = {
      startTime: performance.now(),
      endTime: null,
      duration: null
    };
  }

  endStage(stageName) {
    const stage = this.metrics.stages[stageName];
    if (stage) {
      stage.endTime = performance.now();
      stage.duration = stage.endTime - stage.startTime;
    }
  }

  recordAssetMetrics(assets) {
    this.metrics.assets = {
      totalSize: assets.reduce((sum, asset) => sum + asset.size, 0),
      count: assets.length,
      breakdown: assets.reduce((acc, asset) => {
        const type = asset.name.split('.').pop();
        acc[type] = (acc[type] || 0) + asset.size;
        return acc;
      }, {})
    };
  }

  recordPerformanceMetrics() {
    this.metrics.performance = {
      totalBuildTime: Date.now() - this.metrics.startTime,
      memoryUsage: process.memoryUsage(),
      nodeVersion: process.version
    };
  }

  generateReport() {
    this.recordPerformanceMetrics();
    
    const report = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      ...this.metrics
    };

    console.log('\n📊 Build Metrics Report');
    console.log('========================');
    console.log(`Total Build Time: ${(report.performance.totalBuildTime / 1000).toFixed(2)}s`);
    console.log(`Total Assets: ${report.assets.count}`);
    console.log(`Total Size: ${(report.assets.totalSize / 1024 / 1024).toFixed(2)}MB`);
    
    console.log('\nStage Timings:');
    Object.entries(report.stages).forEach(([stage, metrics]) => {
      console.log(`  ${stage}: ${(metrics.duration / 1000).toFixed(2)}s`);
    });

    return report;
  }

  async saveReport(outputPath) {
    const report = this.generateReport();
    await fs.writeFile(outputPath, JSON.stringify(report, null, 2));
  }
}

export default BuildMetrics;
```
