# Universal CSS System Architecture

## CSS Architecture Strategy

### Modular CSS System
```
Universal CSS System
├── base.css (reset + typography + grid)
├── components/
│   ├── header.css
│   ├── footer.css
│   ├── sidebar.css
│   ├── content.css
│   └── forms.css
├── layouts/
│   ├── single-column.css
│   ├── two-column.css
│   └── three-column.css
└── utilities/
    ├── responsive.css
    ├── spacing.css
    └── colors.css
```

### CSS Compilation Strategy
```
Single Optimized Bundle Strategy:
├── critical.css (inlined in <head>) - 14KB max
└── site.min.css (single file) - All styles combined & minified
    ├── base.css (reset + typography + grid)
    ├── header.css (header component)
    ├── footer.css (footer component)
    ├── components.css (all UI components)
    ├── layouts.css (page layouts)
    └── utilities.css (utility classes)

Build Process: Source CSS → PostCSS → PurgeCSS → Minification → Single Bundle
```

## Base CSS Foundation

### CSS Reset and Normalize
```css
/* base/reset.css */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-primary);
  color: var(--color-text);
  background-color: var(--color-background);
}
```

### Typography System
```css
/* base/typography.css */
:root {
  /* Font families */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-secondary: 'Merriweather', Georgia, serif;
  --font-mono: 'Fira Code', Consolas, monospace;
  
  /* Font sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  
  /* Line heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-primary);
  font-weight: 600;
  line-height: var(--leading-tight);
  margin-bottom: 0.5em;
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }
```

### Grid System
```css
/* base/grid.css */
:root {
  --container-max-width: 1200px;
  --container-padding: 1rem;
  --grid-gap: 1.5rem;
}

.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.grid {
  display: grid;
  gap: var(--grid-gap);
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.flex {
  display: flex;
  gap: var(--grid-gap);
}

.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.justify-center { justify-content: center; }
.items-center { align-items: center; }
```

## Color System

### Color Variables
```css
/* utilities/colors.css */
:root {
  /* Primary colors */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-900: #1e3a8a;
  
  /* Neutral colors */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  /* Semantic colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* Application colors */
  --color-background: var(--color-gray-50);
  --color-surface: #ffffff;
  --color-text: var(--color-gray-900);
  --color-text-muted: var(--color-gray-600);
  --color-border: var(--color-gray-200);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--color-gray-900);
    --color-surface: var(--color-gray-800);
    --color-text: var(--color-gray-100);
    --color-text-muted: var(--color-gray-400);
    --color-border: var(--color-gray-700);
  }
}
```

## Platform-Specific Theming

### Job Portal Theme
```css
/* platform-themes/job-portal-theme.css */
:root {
  --theme-primary: var(--color-primary-600);
  --theme-secondary: var(--color-gray-600);
  --theme-accent: var(--color-success);
  
  /* Job-specific colors */
  --job-featured: #fef3c7;
  --job-urgent: #fee2e2;
  --job-remote: #ecfdf5;
  
  /* Salary colors */
  --salary-high: var(--color-success);
  --salary-medium: var(--color-warning);
  --salary-entry: var(--color-info);
}

.job-card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  padding: 1.5rem;
  transition: box-shadow 0.2s ease;
}

.job-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.job-featured {
  border-left: 4px solid var(--theme-accent);
  background: var(--job-featured);
}
```

### Comparison Site Theme
```css
/* platform-themes/comparison-theme.css */
:root {
  --theme-primary: #7c3aed;
  --theme-secondary: #a855f7;
  --theme-accent: #f59e0b;
  
  /* Rating colors */
  --rating-excellent: var(--color-success);
  --rating-good: #84cc16;
  --rating-average: var(--color-warning);
  --rating-poor: var(--color-error);
  
  /* Price colors */
  --price-budget: var(--color-success);
  --price-mid: var(--color-warning);
  --price-premium: #8b5cf6;
}

.product-card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.75rem;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.product-card:hover {
  transform: translateY(-2px);
}

.rating-stars {
  color: var(--theme-accent);
}
```

## Responsive Design System

### Breakpoint System
```css
/* utilities/responsive.css */
:root {
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Mobile-first approach */
.container {
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }
  
  .grid-sm-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .container {
    padding: 0 2rem;
  }
  
  .grid-md-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .hidden-md {
    display: none;
  }
}

@media (min-width: 1024px) {
  .grid-lg-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .sidebar {
    display: block;
  }
}
```

### Container Queries
```css
/* Modern container-based responsive design */
.job-listings {
  container-type: inline-size;
}

@container (min-width: 400px) {
  .job-card {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 1rem;
    align-items: center;
  }
}

@container (min-width: 600px) {
  .job-card {
    grid-template-columns: auto 1fr auto auto;
  }
  
  .job-meta {
    display: flex;
    gap: 1rem;
  }
}
```

## CSS Compilation and Optimization

### PostCSS Configuration
```javascript
// postcss.config.js
module.exports = {
  plugins: [
    require('postcss-import'),
    require('postcss-custom-properties'),
    require('postcss-nested'),
    require('autoprefixer'),
    require('cssnano')({
      preset: 'default'
    })
  ]
}
```

### CSS Build Process
```css
/* Input: src/css/main.css */
@import 'base/reset.css';
@import 'base/typography.css';
@import 'base/grid.css';
@import 'components/header.css';
@import 'components/footer.css';
@import 'components/navigation.css';
@import 'components/cards.css';
@import 'components/forms.css';
@import 'components/buttons.css';
@import 'layouts/grid.css';
@import 'layouts/flexbox.css';
@import 'utilities/colors.css';
@import 'utilities/spacing.css';
@import 'utilities/responsive.css';

/* Platform-specific imports based on config */
@import 'platform-themes/job-portal-theme.css';

/* Build Pipeline: */
/* 1. PostCSS processing (autoprefixer, custom properties) */
/* 2. PurgeCSS (remove unused styles) */
/* 3. CSSNano minification */
/* 4. Gzip compression */
/* Output: dist/assets/css/site.min.css (single optimized file) */
```

### Critical CSS Strategy
```css
/* critical.css - Above-fold styles */
/* Only essential styles for initial render */
:root { /* CSS variables */ }
body { /* Basic typography */ }
.header { /* Header styles */ }
.hero { /* Hero section */ }
.container { /* Layout container */ }
.grid { /* Basic grid */ }

/* Non-critical styles loaded separately */
/* components/, layouts/, utilities/ */
```

## CSS Performance Optimization

### Bundle Splitting
```javascript
const cssBundles = {
  critical: [
    'base/reset.css',
    'base/typography.css',
    'utilities/colors.css',
    'components/header.css'
  ],
  main: [
    'base/grid.css',
    'utilities/spacing.css',
    'utilities/responsive.css',
    'components/*.css',
    'layouts/*.css'
  ],
  pageSpecific: {
    'job-single': ['pages/job-single.css'],
    'company-profile': ['pages/company-profile.css']
  }
}
```

### CSS Purging
```javascript
// PurgeCSS configuration
const purgeCSSConfig = {
  content: [
    './dist/**/*.html',
    './src/js/**/*.js'
  ],
  css: ['./dist/assets/css/*.css'],
  safelist: [
    /^js-/,
    /^data-/,
    'active',
    'open',
    'visible'
  ]
}
```

### Performance Metrics
```css
/* Target performance goals */
/* Critical CSS: < 14KB (inlined) */
/* Main CSS bundle: < 50KB (gzipped) */
/* Page-specific CSS: < 10KB each */
/* Total CSS payload: < 100KB */
```
