# Dynamic Client-Side Routing System

## Router Architecture Overview

### Hybrid Routing Strategy
```
Static Routes (Pre-generated):
├── /locations/london/                    (Popular location pages)
├── /locations/london/developer/          (Popular job titles)
├── /locations/london/site-manager/       (Popular combinations)
└── /jobs/job-123.html                    (Individual job pages)

Dynamic Routes (Client-side):
├── /locations/london/construction-manager/  (Less common titles)
├── /locations/manchester/ux-designer/       (Less common combinations)
├── /locations/london/site-manager/page-3/   (Deep pagination)
└── /search/?q=remote+developer              (Search results)
```

## Client-Side Router Implementation

### Core Router Class
```javascript
// src/js/core/Router.js
class UniversalRouter {
    constructor() {
        this.routes = new Map();
        this.middlewares = [];
        this.currentRoute = null;
        this.isNavigating = false;
        
        this.initializeRouter();
        this.registerRoutes();
    }
    
    initializeRouter() {
        // Handle browser navigation
        window.addEventListener('popstate', this.handlePopState.bind(this));
        
        // Intercept link clicks
        document.addEventListener('click', this.handleLinkClick.bind(this));
        
        // Handle initial page load
        this.handleRoute(window.location.pathname);
    }
    
    registerRoutes() {
        // Dynamic job title routes
        this.addRoute(/^\/locations\/([^\/]+)\/([^\/]+)\/?$/, this.handleJobTitlePage.bind(this));
        
        // Dynamic pagination routes
        this.addRoute(/^\/locations\/([^\/]+)\/([^\/]+)\/page\/(\d+)\/?$/, this.handleJobTitlePagination.bind(this));
        
        // Search routes
        this.addRoute(/^\/search\/?$/, this.handleSearchPage.bind(this));
        
        // Category routes
        this.addRoute(/^\/categories\/([^\/]+)\/?$/, this.handleCategoryPage.bind(this));
    }
    
    addRoute(pattern, handler) {
        this.routes.set(pattern, handler);
    }
    
    addMiddleware(middleware) {
        this.middlewares.push(middleware);
    }
    
    async handleRoute(path) {
        if (this.isNavigating) return;
        
        this.isNavigating = true;
        
        try {
            // Run middlewares
            for (const middleware of this.middlewares) {
                const result = await middleware(path);
                if (result === false) {
                    this.isNavigating = false;
                    return;
                }
            }
            
            // Find matching route
            for (const [pattern, handler] of this.routes) {
                const match = path.match(pattern);
                if (match) {
                    await handler(match);
                    this.isNavigating = false;
                    return;
                }
            }
            
            // No route found - check if static page exists
            await this.handleStaticFallback(path);
            
        } catch (error) {
            console.error('Router error:', error);
            this.handleError(error);
        } finally {
            this.isNavigating = false;
        }
    }
    
    async handleJobTitlePage(match) {
        const [, location, jobTitle] = match;
        
        // Show skeleton loading
        this.showSkeletonLoading('job-title-page');
        
        try {
            // Try to fetch from static API first
            const response = await fetch(`/api/locations/${location}/${jobTitle}.json`);
            
            if (response.ok) {
                const data = await response.json();
                await this.renderJobTitlePage(location, jobTitle, data);
            } else {
                // Fallback to WordPress API
                await this.fetchFromWordPressAPI(location, jobTitle);
            }
            
        } catch (error) {
            // Ultimate fallback: redirect to location page
            window.location.href = `/locations/${location}/`;
        }
    }
    
    async fetchFromWordPressAPI(location, jobTitle) {
        const response = await fetch(`/manager/wp-json/universal/v1/jobs/${location}/${jobTitle}`);
        const data = await response.json();
        
        // Cache the response as static API
        await this.cacheAPIResponse(location, jobTitle, data);
        
        await this.renderJobTitlePage(location, jobTitle, data);
    }
    
    async renderJobTitlePage(location, jobTitle, data) {
        const container = document.getElementById('main-content');
        const title = this.formatJobTitle(jobTitle);
        const locationName = this.formatLocation(location);
        
        // Update page meta
        this.updatePageMeta({
            title: `${title} Jobs in ${locationName} - ${data.total} Positions`,
            description: `Find ${data.total} ${title.toLowerCase()} jobs in ${locationName}. Apply to top companies.`,
            canonical: `/locations/${location}/${jobTitle}/`
        });
        
        // Render page content
        container.innerHTML = await this.getJobTitlePageTemplate(location, jobTitle, data);
        
        // Initialize dynamic components
        await this.initializeDynamicComponents();
        
        // Hide skeleton loading
        this.hideSkeletonLoading();
    }
    
    async getJobTitlePageTemplate(location, jobTitle, data) {
        const title = this.formatJobTitle(jobTitle);
        const locationName = this.formatLocation(location);
        
        return `
            <div class="dynamic-page" data-page-type="job-title">
                <!-- AdSense placeholder with skeleton -->
                <div class="ad-container ad-container--header">
                    <div class="ad-skeleton" data-ad-slot="header"></div>
                </div>
                
                <div class="page-header">
                    <h1 class="page-title">${title} Jobs in ${locationName}</h1>
                    <p class="job-count">${data.total} jobs found</p>
                    
                    <nav class="breadcrumbs" itemscope itemtype="https://schema.org/BreadcrumbList">
                        <a href="/" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                            <span itemprop="name">Home</span>
                            <meta itemprop="position" content="1">
                        </a> → 
                        <a href="/locations/${location}/" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                            <span itemprop="name">${locationName}</span>
                            <meta itemprop="position" content="2">
                        </a> → 
                        <span itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                            <span itemprop="name">${title} Jobs</span>
                            <meta itemprop="position" content="3">
                        </span>
                    </nav>
                </div>
                
                <!-- Job listings with skeleton placeholders -->
                <div class="job-listings" id="job-listings">
                    ${data.jobs.map((job, index) => this.renderJobCard(job, index)).join('')}
                </div>
                
                <!-- AdSense in-content ad -->
                <div class="ad-container ad-container--content">
                    <div class="ad-skeleton" data-ad-slot="content"></div>
                </div>
                
                <!-- Pagination -->
                <nav class="pagination" id="pagination">
                    ${this.renderPagination(data.pagination, location, jobTitle)}
                </nav>
                
                <!-- Bottom AdSense -->
                <div class="ad-container ad-container--footer">
                    <div class="ad-skeleton" data-ad-slot="footer"></div>
                </div>
            </div>
        `;
    }
    
    renderJobCard(job, index) {
        // Add AdSense after every 5th job
        const adSense = (index + 1) % 5 === 0 ? `
            <div class="ad-container ad-container--inline">
                <div class="ad-skeleton" data-ad-slot="inline-${Math.floor(index / 5)}"></div>
            </div>
        ` : '';
        
        return `
            <article class="job-card" data-job-id="${job.id}" itemscope itemtype="https://schema.org/JobPosting">
                <div class="job-card__header">
                    <h3 class="job-card__title" itemprop="title">
                        <a href="/jobs/${job.slug}.html" class="job-card__link">${job.title}</a>
                    </h3>
                    <div class="job-card__company" itemprop="hiringOrganization" itemscope itemtype="https://schema.org/Organization">
                        <span itemprop="name">${job.company}</span>
                    </div>
                </div>
                
                <div class="job-card__meta">
                    <span class="job-card__location" itemprop="jobLocation" itemscope itemtype="https://schema.org/Place">
                        <span itemprop="name">${job.location}</span>
                    </span>
                    <span class="job-card__salary" itemprop="baseSalary" itemscope itemtype="https://schema.org/MonetaryAmount">
                        <span itemprop="value">${job.salary}</span>
                    </span>
                    <span class="job-card__type" itemprop="employmentType">${job.type}</span>
                </div>
                
                <div class="job-card__description" itemprop="description">
                    ${job.excerpt}
                </div>
                
                <div class="job-card__footer">
                    <div class="job-card__tags">
                        ${job.skills.map(skill => `<span class="tag" itemprop="skills">${skill}</span>`).join('')}
                    </div>
                    <div class="job-card__actions">
                        <div class="save-job-mount" data-job-id="${job.id}"></div>
                        <a href="/jobs/${job.slug}.html" class="btn btn--primary">View Details</a>
                    </div>
                </div>
                
                <meta itemprop="datePosted" content="${job.posted_date}">
            </article>
            ${adSense}
        `;
    }
    
    handleLinkClick(event) {
        const link = event.target.closest('a');
        if (!link) return;
        
        const href = link.getAttribute('href');
        if (!href || href.startsWith('http') || href.startsWith('mailto') || href.startsWith('tel')) {
            return;
        }
        
        event.preventDefault();
        this.navigate(href);
    }
    
    navigate(path) {
        if (path === window.location.pathname) return;
        
        history.pushState(null, '', path);
        this.handleRoute(path);
    }
    
    handlePopState(event) {
        this.handleRoute(window.location.pathname);
    }
    
    updatePageMeta(meta) {
        document.title = meta.title;
        
        // Update meta description
        let metaDesc = document.querySelector('meta[name="description"]');
        if (metaDesc) {
            metaDesc.setAttribute('content', meta.description);
        }
        
        // Update canonical URL
        let canonical = document.querySelector('link[rel="canonical"]');
        if (canonical) {
            canonical.setAttribute('href', window.location.origin + meta.canonical);
        }
    }
    
    formatJobTitle(jobTitle) {
        return jobTitle.split('-').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    }
    
    formatLocation(location) {
        return location.split('-').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    }
}

// Initialize router
const router = new UniversalRouter();

// Add analytics middleware
router.addMiddleware(async (path) => {
    if (window.gtag) {
        gtag('config', 'GA_MEASUREMENT_ID', {
            page_path: path
        });
    }
    return true;
});

// Add performance monitoring middleware
router.addMiddleware(async (path) => {
    performance.mark('route-start');
    return true;
});

export default router;
```

## Route Caching Strategy

### Service Worker Route Caching
```javascript
// src/js/workers/route-cache-worker.js
class RouteCacheWorker {
    constructor() {
        this.CACHE_NAME = 'dynamic-routes-v1';
        this.API_CACHE_NAME = 'api-responses-v1';
        this.CACHE_DURATION = 30 * 60 * 1000; // 30 minutes
    }
    
    async cacheRoute(path, content) {
        const cache = await caches.open(this.CACHE_NAME);
        const response = new Response(content, {
            headers: {
                'Content-Type': 'text/html',
                'Cache-Control': 'max-age=1800' // 30 minutes
            }
        });
        
        await cache.put(path, response);
    }
    
    async getCachedRoute(path) {
        const cache = await caches.open(this.CACHE_NAME);
        const response = await cache.match(path);
        
        if (response) {
            const cachedTime = response.headers.get('cached-time');
            if (cachedTime && Date.now() - parseInt(cachedTime) < this.CACHE_DURATION) {
                return response;
            }
        }
        
        return null;
    }
    
    async cacheAPIResponse(url, data) {
        const cache = await caches.open(this.API_CACHE_NAME);
        const response = new Response(JSON.stringify(data), {
            headers: {
                'Content-Type': 'application/json',
                'cached-time': Date.now().toString()
            }
        });
        
        await cache.put(url, response);
    }
}
```

## Performance Optimization

### Route Preloading
```javascript
// src/js/core/RoutePreloader.js
class RoutePreloader {
    constructor(router) {
        this.router = router;
        this.preloadQueue = new Set();
        this.intersectionObserver = new IntersectionObserver(
            this.handleIntersection.bind(this),
            { rootMargin: '50px' }
        );
        
        this.initializePreloading();
    }
    
    initializePreloading() {
        // Preload routes on hover
        document.addEventListener('mouseover', this.handleLinkHover.bind(this));
        
        // Preload routes in viewport
        this.observeLinks();
    }
    
    handleLinkHover(event) {
        const link = event.target.closest('a[href^="/locations/"]');
        if (link && !this.preloadQueue.has(link.href)) {
            this.preloadRoute(link.href);
        }
    }
    
    async preloadRoute(path) {
        if (this.preloadQueue.has(path)) return;
        
        this.preloadQueue.add(path);
        
        try {
            // Extract location and job title from path
            const match = path.match(/^\/locations\/([^\/]+)\/([^\/]+)\/?$/);
            if (match) {
                const [, location, jobTitle] = match;
                
                // Preload API data
                const apiUrl = `/api/locations/${location}/${jobTitle}.json`;
                fetch(apiUrl).catch(() => {
                    // Fallback to WordPress API
                    fetch(`/manager/wp-json/universal/v1/jobs/${location}/${jobTitle}`);
                });
            }
        } catch (error) {
            console.warn('Preload failed:', error);
        }
    }
    
    observeLinks() {
        document.querySelectorAll('a[href^="/locations/"]').forEach(link => {
            this.intersectionObserver.observe(link);
        });
    }
    
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.preloadRoute(entry.target.href);
                this.intersectionObserver.unobserve(entry.target);
            }
        });
    }
}
```
