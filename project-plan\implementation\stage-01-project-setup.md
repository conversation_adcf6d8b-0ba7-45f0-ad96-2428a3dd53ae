# Stage 1: Project Structure & Environment Setup

## Stage Overview
Set up the complete project structure, development environment, and foundational files following the modular architecture with 150-line file limits.

## Prerequisites
- PHP 8.2+ installed
- Node.js 18+ installed
- Composer installed
- WordPress 6.4+ downloaded
- Git initialized

## Implementation Steps

### Step 1.1: Create Root Directory Structure
```bash
# Create main project directory
mkdir universal-app
cd universal-app

# Create main directories
mkdir -p manager/wp-content/themes/universal-theme
mkdir -p manager/wp-content/plugins/universal-jobs
mkdir -p components
mkdir -p assets/{css,js,images,fonts}
mkdir -p jobs
mkdir -p companies
mkdir -p locations
mkdir -p api
mkdir -p src/{css,js,components}
mkdir -p scripts
mkdir -p tests/{unit,integration,feature}
mkdir -p docs
mkdir -p logs
```

### Step 1.2: Initialize Package Management
```bash
# Initialize npm for frontend dependencies
npm init -y

# Initialize composer for PHP dependencies
composer init --name="universal-app/core" --type="project"
```

### Step 1.3: Create Core Configuration Files

#### package.json
```json
{
  "name": "universal-app",
  "version": "1.0.0",
  "description": "Universal App - Modular Job Portal System",
  "main": "index.js",
  "scripts": {
    "dev": "webpack serve --mode development",
    "build": "webpack --mode production",
    "build:css": "node scripts/build-css.js",
    "build:components": "node scripts/build-components.js",
    "build:api": "node scripts/build-api.js",
    "test": "jest",
    "lint": "eslint src/",
    "format": "prettier --write src/"
  },
  "devDependencies": {
    "webpack": "^5.89.0",
    "webpack-cli": "^5.1.4",
    "webpack-dev-server": "^4.15.1",
    "css-loader": "^6.8.1",
    "mini-css-extract-plugin": "^2.7.6",
    "postcss": "^8.4.32",
    "postcss-loader": "^7.3.3",
    "autoprefixer": "^10.4.16",
    "cssnano": "^6.0.1",
    "babel-loader": "^9.1.3",
    "@babel/core": "^7.23.5",
    "@babel/preset-env": "^7.23.5",
    "eslint": "^8.55.0",
    "prettier": "^3.1.0",
    "jest": "^29.7.0"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0"
  }
}
```

#### composer.json
```json
{
  "name": "universal-app/core",
  "type": "project",
  "description": "Universal App Core System",
  "require": {
    "php": ">=8.2",
    "psr/container": "^2.0",
    "psr/log": "^3.0",
    "symfony/dependency-injection": "^6.4",
    "twig/twig": "^3.8"
  },
  "require-dev": {
    "phpunit/phpunit": "^10.5",
    "phpstan/phpstan": "^1.10",
    "squizlabs/php_codesniffer": "^3.8"
  },
  "autoload": {
    "psr-4": {
      "UniversalApp\\Theme\\": "manager/wp-content/themes/universal-theme/src/",
      "UniversalApp\\Plugin\\": "manager/wp-content/plugins/universal-jobs/src/"
    }
  },
  "autoload-dev": {
    "psr-4": {
      "UniversalApp\\Tests\\": "tests/"
    }
  }
}
```

### Step 1.4: Create Build Configuration Files

#### webpack.config.js
```javascript
const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const isDevelopment = process.env.NODE_ENV === 'development';

module.exports = {
  entry: {
    main: './src/js/main.js',
    critical: './src/css/critical.css'
  },
  
  output: {
    path: path.resolve(__dirname, 'assets'),
    filename: isDevelopment ? 'js/[name].js' : 'js/[name].min.js',
    clean: true
  },
  
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env']
          }
        }
      },
      {
        test: /\.css$/,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
          'postcss-loader'
        ]
      }
    ]
  },
  
  plugins: [
    new MiniCssExtractPlugin({
      filename: isDevelopment ? 'css/[name].css' : 'css/[name].min.css'
    })
  ],
  
  devServer: {
    static: {
      directory: path.join(__dirname, 'assets')
    },
    port: 3000,
    hot: true
  }
};
```

#### postcss.config.js
```javascript
module.exports = {
  plugins: [
    require('autoprefixer'),
    require('cssnano')({
      preset: 'default'
    })
  ]
};
```

### Step 1.5: Create Development Configuration Files

#### .env.example
```env
# WordPress Configuration
WP_HOME=http://localhost/universal-app
WP_SITEURL=http://localhost/universal-app/manager
DB_NAME=universal_app
DB_USER=root
DB_PASSWORD=
DB_HOST=localhost

# Static Site Configuration
STATIC_SITE_URL=http://localhost/universal-app
API_BASE_URL=http://localhost/universal-app/api

# Build Configuration
NODE_ENV=development
BUILD_TARGET=development

# AdSense Configuration
ADSENSE_CLIENT_ID=
ADSENSE_AUTO_ADS=false

# Performance Configuration
ENABLE_CACHING=true
CACHE_DURATION=3600
```

#### .gitignore
```gitignore
# Dependencies
node_modules/
vendor/

# Build outputs
assets/css/*.min.css
assets/js/*.min.js
assets/images/optimized/

# WordPress
manager/wp-config.php
manager/wp-content/uploads/
manager/wp-content/cache/

# Environment files
.env
.env.local

# Logs
logs/*.log
*.log

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Testing
coverage/
.nyc_output/

# Temporary files
tmp/
temp/
```

### Step 1.6: Create Initial Source Files

#### src/css/main.css
```css
/* Main CSS entry point */
@import 'base/reset.css';
@import 'base/variables.css';
@import 'base/typography.css';
@import 'components/header.css';
@import 'components/footer.css';
@import 'components/navigation.css';
@import 'layouts/grid.css';
@import 'utilities/responsive.css';
```

#### src/css/critical.css
```css
/* Critical CSS for above-the-fold content */
:root {
  --color-primary: #2563eb;
  --color-secondary: #64748b;
  --color-background: #ffffff;
  --color-text: #1e293b;
  --font-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
  font-family: var(--font-primary);
  color: var(--color-text);
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
}

.header {
  background: var(--color-background);
  border-bottom: 1px solid #e2e8f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}
```

#### src/js/main.js
```javascript
// Main JavaScript entry point
import './modules/router.js';
import './modules/components.js';
import './modules/performance.js';

// Initialize application
document.addEventListener('DOMContentLoaded', () => {
  console.log('Universal App initialized');
  
  // Initialize core modules
  if (window.UniversalApp) {
    window.UniversalApp.init();
  }
});
```

### Step 1.7: Create Basic Build Scripts

#### scripts/build-css.js
```javascript
#!/usr/bin/env node
const fs = require('fs').promises;
const path = require('path');

async function buildCSS() {
  console.log('🎨 Building CSS...');
  
  try {
    // This will be expanded in later stages
    console.log('✅ CSS build completed');
  } catch (error) {
    console.error('❌ CSS build failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  buildCSS();
}

module.exports = buildCSS;
```

### Step 1.8: Create Testing Configuration

#### phpunit.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit bootstrap="tests/bootstrap.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false">
    <testsuites>
        <testsuite name="Unit Tests">
            <directory>tests/unit</directory>
        </testsuite>
        <testsuite name="Integration Tests">
            <directory>tests/integration</directory>
        </testsuite>
    </testsuites>
    <coverage>
        <include>
            <directory suffix=".php">manager/wp-content/themes/universal-theme/src</directory>
            <directory suffix=".php">manager/wp-content/plugins/universal-jobs/src</directory>
        </include>
    </coverage>
</phpunit>
```

#### jest.config.js
```javascript
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: [
    '<rootDir>/tests/**/*.test.js'
  ],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/**/*.min.js'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html']
};
```

## Testing Checklist

### Environment Verification
- [ ] PHP 8.2+ is installed and working
- [ ] Node.js 18+ is installed and working
- [ ] Composer is installed and working
- [ ] All directories are created correctly
- [ ] Package.json and composer.json are valid
- [ ] Git repository is initialized

### Build System Testing
- [ ] `npm install` runs without errors
- [ ] `composer install` runs without errors
- [ ] `npm run build` creates asset files
- [ ] `npm run dev` starts development server
- [ ] CSS compilation works
- [ ] JavaScript compilation works

### File Structure Validation
- [ ] All required directories exist
- [ ] Configuration files are in place
- [ ] Source files are created
- [ ] Build scripts are executable
- [ ] Testing configuration is set up

## Success Criteria
1. ✅ Complete project structure is created
2. ✅ All configuration files are in place
3. ✅ Package management is working
4. ✅ Build system compiles assets
5. ✅ Development server runs
6. ✅ Testing framework is configured
7. ✅ Git repository is initialized
8. ✅ Environment variables are documented

## Next Stage
Once Stage 1 is complete and all tests pass, proceed to **Stage 2: WordPress Theme Foundation** where we'll create the basic WordPress theme structure and core PHP classes.
