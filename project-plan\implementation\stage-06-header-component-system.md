# Stage 6: Header Component System

## Stage Overview
Implement the complete header component system with WordPress admin configuration, static HTML generation, and responsive design. This is our first major component following the modular architecture.

## Prerequisites
- Stages 1-5 completed successfully
- WordPress theme foundation working
- Configuration system operational
- Asset management system ready

## Implementation Steps

### Step 6.1: Create Header Configuration Schema

#### manager/wp-content/themes/universal-theme/src/Config/HeaderConfig.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Theme\Config;

/**
 * Header Configuration Schema
 * 
 * Defines the structure and validation for header settings
 * 
 * @package UniversalApp\Theme\Config
 * @since 1.0.0
 */
final class HeaderConfig
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = $this->mergeWithDefaults($config);
        $this->validate();
    }

    /**
     * Get configuration value
     */
    public function get(string $key, mixed $default = null): mixed
    {
        return $this->config[$key] ?? $default;
    }

    /**
     * Set configuration value
     */
    public function set(string $key, mixed $value): void
    {
        $this->config[$key] = $value;
        $this->validate();
    }

    /**
     * Get all configuration
     */
    public function getAll(): array
    {
        return $this->config;
    }

    /**
     * Merge with default configuration
     */
    private function mergeWithDefaults(array $config): array
    {
        $defaults = [
            'logo' => [
                'text' => get_bloginfo('name'),
                'image_url' => '',
                'image_alt' => get_bloginfo('name'),
                'link_url' => home_url('/'),
                'width' => 200,
                'height' => 60
            ],
            'navigation' => [
                'menu_location' => 'primary',
                'show_search' => true,
                'show_cta_button' => true,
                'cta_text' => 'Post Job',
                'cta_url' => '/post-job/',
                'mobile_breakpoint' => 768
            ],
            'layout' => [
                'container_width' => 1200,
                'sticky' => true,
                'background_color' => '#ffffff',
                'text_color' => '#1e293b',
                'border_bottom' => true,
                'shadow' => false
            ],
            'search' => [
                'placeholder' => 'Search jobs...',
                'show_filters' => true,
                'autocomplete' => true,
                'min_chars' => 2
            ]
        ];

        return array_merge_recursive($defaults, $config);
    }

    /**
     * Validate configuration
     */
    private function validate(): void
    {
        $required = ['logo', 'navigation', 'layout'];
        
        foreach ($required as $key) {
            if (!isset($this->config[$key])) {
                throw new \InvalidArgumentException("Missing required header config: {$key}");
            }
        }

        // Validate logo dimensions
        if ($this->config['logo']['width'] < 50 || $this->config['logo']['width'] > 400) {
            throw new \InvalidArgumentException('Logo width must be between 50 and 400 pixels');
        }

        // Validate container width
        if ($this->config['layout']['container_width'] < 800 || $this->config['layout']['container_width'] > 1600) {
            throw new \InvalidArgumentException('Container width must be between 800 and 1600 pixels');
        }
    }

    /**
     * Export configuration for static generation
     */
    public function toArray(): array
    {
        return $this->config;
    }
}
```

### Step 6.2: Create Header Service

#### manager/wp-content/themes/universal-theme/src/Services/HeaderService.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Theme\Services;

use UniversalApp\Theme\Config\HeaderConfig;

/**
 * Header Service
 * 
 * Manages header configuration and generation
 * 
 * @package UniversalApp\Theme\Services
 * @since 1.0.0
 */
final class HeaderService
{
    private HeaderConfig $config;
    private string $outputPath;

    public function __construct()
    {
        $this->outputPath = ABSPATH . '../components/';
        $this->loadConfiguration();
    }

    /**
     * Generate static header component
     */
    public function generateStaticHeader(): bool
    {
        try {
            $html = $this->renderHeaderHTML();
            $css = $this->generateHeaderCSS();
            
            // Ensure output directory exists
            if (!is_dir($this->outputPath)) {
                mkdir($this->outputPath, 0755, true);
            }

            // Write header HTML
            file_put_contents($this->outputPath . 'header.html', $html);
            
            // Write header CSS
            file_put_contents($this->outputPath . 'header.css', $css);

            return true;
        } catch (\Exception $e) {
            error_log('Header generation failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Render header HTML
     */
    private function renderHeaderHTML(): string
    {
        $logo = $this->config->get('logo');
        $navigation = $this->config->get('navigation');
        $layout = $this->config->get('layout');
        $search = $this->config->get('search');

        $stickyClass = $layout['sticky'] ? ' header--sticky' : '';
        $containerWidth = $layout['container_width'];

        ob_start();
        ?>
        <header class="header<?php echo $stickyClass; ?>" role="banner">
            <div class="header__container" style="max-width: <?php echo $containerWidth; ?>px;">
                <div class="header__brand">
                    <?php if ($logo['image_url']): ?>
                        <a href="<?php echo esc_url($logo['link_url']); ?>" class="header__logo-link">
                            <img src="<?php echo esc_url($logo['image_url']); ?>" 
                                 alt="<?php echo esc_attr($logo['image_alt']); ?>"
                                 width="<?php echo $logo['width']; ?>"
                                 height="<?php echo $logo['height']; ?>"
                                 class="header__logo-image" />
                        </a>
                    <?php else: ?>
                        <a href="<?php echo esc_url($logo['link_url']); ?>" class="header__logo-text">
                            <?php echo esc_html($logo['text']); ?>
                        </a>
                    <?php endif; ?>
                </div>

                <nav class="header__navigation" role="navigation" aria-label="Main navigation">
                    <div class="header__nav-menu" id="header-nav-menu">
                        <?php echo $this->getNavigationMenu($navigation['menu_location']); ?>
                    </div>
                </nav>

                <div class="header__actions">
                    <?php if ($navigation['show_search']): ?>
                        <div class="header__search">
                            <form class="search-form" role="search" method="get" action="/">
                                <label class="search-form__label" for="header-search">
                                    <span class="screen-reader-text">Search</span>
                                </label>
                                <input type="search" 
                                       id="header-search"
                                       class="search-form__input"
                                       placeholder="<?php echo esc_attr($search['placeholder']); ?>"
                                       name="s"
                                       autocomplete="off"
                                       data-min-chars="<?php echo $search['min_chars']; ?>" />
                                <button type="submit" class="search-form__submit">
                                    <span class="screen-reader-text">Search</span>
                                    <svg class="search-form__icon" width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>

                    <?php if ($navigation['show_cta_button']): ?>
                        <a href="<?php echo esc_url($navigation['cta_url']); ?>" class="header__cta-button">
                            <?php echo esc_html($navigation['cta_text']); ?>
                        </a>
                    <?php endif; ?>

                    <button class="header__mobile-toggle" 
                            aria-controls="header-nav-menu" 
                            aria-expanded="false"
                            data-breakpoint="<?php echo $navigation['mobile_breakpoint']; ?>">
                        <span class="header__mobile-toggle-line"></span>
                        <span class="header__mobile-toggle-line"></span>
                        <span class="header__mobile-toggle-line"></span>
                        <span class="screen-reader-text">Toggle navigation</span>
                    </button>
                </div>
            </div>
        </header>
        <?php
        return ob_get_clean();
    }

    /**
     * Generate header CSS
     */
    private function generateHeaderCSS(): string
    {
        $layout = $this->config->get('layout');
        $navigation = $this->config->get('navigation');

        $css = "
        /* Header Component Styles */
        .header {
            background-color: {$layout['background_color']};
            color: {$layout['text_color']};
            position: relative;
            z-index: 100;
        }

        .header--sticky {
            position: sticky;
            top: 0;
        }

        .header__container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            margin: 0 auto;
        }

        .header__brand {
            flex-shrink: 0;
        }

        .header__logo-link,
        .header__logo-text {
            display: inline-block;
            text-decoration: none;
            color: inherit;
        }

        .header__logo-image {
            display: block;
            max-width: 100%;
            height: auto;
        }

        .header__logo-text {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .header__navigation {
            flex: 1;
            margin: 0 2rem;
        }

        .header__nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 2rem;
        }

        .header__actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header__search {
            position: relative;
        }

        .search-form {
            display: flex;
            align-items: center;
            position: relative;
        }

        .search-form__input {
            padding: 0.5rem 2.5rem 0.5rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            width: 200px;
            transition: width 0.3s ease;
        }

        .search-form__input:focus {
            outline: none;
            border-color: #3b82f6;
            width: 250px;
        }

        .search-form__submit {
            position: absolute;
            right: 0.5rem;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.25rem;
            color: #6b7280;
        }

        .header__cta-button {
            background-color: #3b82f6;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .header__cta-button:hover {
            background-color: #2563eb;
        }

        .header__mobile-toggle {
            display: none;
            flex-direction: column;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.5rem;
            gap: 0.25rem;
        }

        .header__mobile-toggle-line {
            width: 1.5rem;
            height: 2px;
            background-color: currentColor;
            transition: all 0.3s ease;
        }

        .screen-reader-text {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Responsive Design */
        @media (max-width: {$navigation['mobile_breakpoint']}px) {
            .header__navigation {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: {$layout['background_color']};
                border-top: 1px solid #e5e7eb;
                padding: 1rem;
            }

            .header__navigation.is-open {
                display: block;
            }

            .header__nav-menu {
                flex-direction: column;
                gap: 1rem;
            }

            .header__mobile-toggle {
                display: flex;
            }

            .header__search {
                order: -1;
            }

            .search-form__input {
                width: 150px;
            }

            .search-form__input:focus {
                width: 180px;
            }
        }
        ";

        if ($layout['border_bottom']) {
            $css .= "\n.header { border-bottom: 1px solid #e5e7eb; }";
        }

        if ($layout['shadow']) {
            $css .= "\n.header { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); }";
        }

        return $css;
    }

    /**
     * Get navigation menu HTML
     */
    private function getNavigationMenu(string $location): string
    {
        $menu = wp_nav_menu([
            'theme_location' => $location,
            'container' => false,
            'menu_class' => 'nav-menu',
            'echo' => false,
            'fallback_cb' => [$this, 'fallbackMenu']
        ]);

        return $menu ?: $this->fallbackMenu();
    }

    /**
     * Fallback menu when no menu is assigned
     */
    public function fallbackMenu(): string
    {
        return '<ul class="nav-menu">
            <li><a href="/">Home</a></li>
            <li><a href="/jobs/">Jobs</a></li>
            <li><a href="/companies/">Companies</a></li>
            <li><a href="/about/">About</a></li>
        </ul>';
    }

    /**
     * Load configuration from WordPress options
     */
    private function loadConfiguration(): void
    {
        $config = get_option('universal_header_config', []);
        $this->config = new HeaderConfig($config);
    }

    /**
     * Save configuration to WordPress options
     */
    public function saveConfiguration(array $config): bool
    {
        try {
            $this->config = new HeaderConfig($config);
            update_option('universal_header_config', $this->config->getAll());
            
            // Regenerate static header
            return $this->generateStaticHeader();
        } catch (\Exception $e) {
            error_log('Header config save failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get current configuration
     */
    public function getConfiguration(): HeaderConfig
    {
        return $this->config;
    }
}
```

### Step 6.3: Create Header Admin Interface

#### manager/wp-content/themes/universal-theme/src/Admin/HeaderAdmin.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Theme\Admin;

use UniversalApp\Theme\Services\HeaderService;

/**
 * Header Admin Interface
 * 
 * Provides WordPress admin interface for header configuration
 * 
 * @package UniversalApp\Theme\Admin
 * @since 1.0.0
 */
final class HeaderAdmin
{
    private HeaderService $headerService;

    public function __construct(HeaderService $headerService)
    {
        $this->headerService = $headerService;
        $this->init();
    }

    /**
     * Initialize admin interface
     */
    public function init(): void
    {
        add_action('admin_menu', [$this, 'addAdminMenu']);
        add_action('admin_init', [$this, 'registerSettings']);
        add_action('wp_ajax_save_header_config', [$this, 'handleAjaxSave']);
        add_action('wp_ajax_preview_header', [$this, 'handleAjaxPreview']);
    }

    /**
     * Add admin menu
     */
    public function addAdminMenu(): void
    {
        add_submenu_page(
            'universal-theme-settings',
            __('Header Settings', 'universal-theme'),
            __('Header', 'universal-theme'),
            'manage_options',
            'universal-header-settings',
            [$this, 'renderSettingsPage']
        );
    }

    /**
     * Register settings
     */
    public function registerSettings(): void
    {
        register_setting('universal_header_settings', 'universal_header_config', [
            'type' => 'array',
            'sanitize_callback' => [$this, 'sanitizeConfig']
        ]);
    }

    /**
     * Render settings page
     */
    public function renderSettingsPage(): void
    {
        $config = $this->headerService->getConfiguration();
        include UNIVERSAL_THEME_PATH . '/templates/admin/header-settings.php';
    }

    /**
     * Handle AJAX save
     */
    public function handleAjaxSave(): void
    {
        check_ajax_referer('universal_header_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $config = $_POST['config'] ?? [];
        $success = $this->headerService->saveConfiguration($config);

        wp_send_json([
            'success' => $success,
            'message' => $success ? 'Header saved successfully' : 'Failed to save header'
        ]);
    }

    /**
     * Handle AJAX preview
     */
    public function handleAjaxPreview(): void
    {
        check_ajax_referer('universal_header_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        // Generate preview HTML (implementation would go here)
        wp_send_json([
            'success' => true,
            'html' => '<div>Preview HTML would go here</div>'
        ]);
    }

    /**
     * Sanitize configuration
     */
    public function sanitizeConfig(array $config): array
    {
        // Sanitization logic would go here
        return $config;
    }
}
```

## Testing Checklist

### Configuration System
- [ ] HeaderConfig class validates input correctly
- [ ] Default configuration is properly merged
- [ ] Configuration can be saved and loaded
- [ ] Invalid configuration throws appropriate errors

### Header Service
- [ ] Static header HTML is generated correctly
- [ ] Header CSS is compiled properly
- [ ] Files are written to correct location
- [ ] Navigation menu integration works
- [ ] Responsive design is implemented

### Admin Interface
- [ ] Header settings page is accessible
- [ ] Configuration form works
- [ ] AJAX save functionality works
- [ ] Preview functionality works
- [ ] Settings are persisted correctly

### Generated Output
- [ ] Header HTML is valid and semantic
- [ ] CSS is properly formatted
- [ ] Responsive breakpoints work
- [ ] Accessibility features are present
- [ ] Performance is optimized

## Success Criteria
1. ✅ Header configuration system is working
2. ✅ Static header component is generated
3. ✅ Admin interface is functional
4. ✅ Responsive design is implemented
5. ✅ Accessibility standards are met
6. ✅ Performance is optimized
7. ✅ All files under 150 lines
8. ✅ Live testing passes

## Next Stage
Once Stage 6 is complete and all tests pass, proceed to **Stage 7: Footer Component System** where we'll implement the footer component following the same modular pattern established with the header.
