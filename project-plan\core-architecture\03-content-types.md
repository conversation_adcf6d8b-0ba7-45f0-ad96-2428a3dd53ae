# Content Types and Field Structures

## Content Type Architecture
Dynamic content type system that allows platform-specific data structures through configuration.

## Field Type System

### Basic Field Types
```json
{
  "text": {
    "description": "Single line text input",
    "validation": ["required", "min_length", "max_length", "pattern"],
    "attributes": ["placeholder", "default_value", "searchable"]
  },
  "textarea": {
    "description": "Multi-line text input",
    "validation": ["required", "min_length", "max_length"],
    "attributes": ["rows", "placeholder", "default_value"]
  },
  "wysiwyg": {
    "description": "Rich text editor",
    "validation": ["required", "min_length", "max_length"],
    "attributes": ["toolbar_options", "allowed_tags", "searchable"]
  }
}
```

### Specialized Field Types
```json
{
  "select": {
    "description": "Dropdown selection",
    "required_attributes": ["options"],
    "validation": ["required"],
    "attributes": ["multiple", "default_value", "filterable"]
  },
  "number": {
    "description": "Numeric input",
    "validation": ["required", "min", "max", "step"],
    "attributes": ["default_value", "filterable", "sortable"]
  },
  "boolean": {
    "description": "True/false checkbox",
    "validation": ["required"],
    "attributes": ["default_value", "filterable"]
  }
}
```

### Media Field Types
```json
{
  "image": {
    "description": "Single image upload",
    "validation": ["required", "file_size", "dimensions"],
    "attributes": ["alt_text", "caption", "sizes"]
  },
  "gallery": {
    "description": "Multiple image upload",
    "validation": ["required", "max_images", "file_size"],
    "attributes": ["thumbnail_size", "lightbox"]
  },
  "file": {
    "description": "File upload",
    "validation": ["required", "file_size", "file_types"],
    "attributes": ["allowed_extensions", "download_link"]
  }
}
```

## Content Type Examples

### Job Portal Content Types
```json
{
  "jobs": {
    "singular": "job",
    "plural": "jobs",
    "slug_pattern": "{title}-{company}-{location}",
    "url_structure": "/jobs/{slug}.html",
    "archive_url": "/jobs/",
    "fields": [
      {
        "name": "title",
        "type": "text",
        "label": "Job Title",
        "required": true,
        "searchable": true,
        "max_length": 100
      },
      {
        "name": "company",
        "type": "text",
        "label": "Company Name",
        "required": true,
        "searchable": true,
        "max_length": 80
      },
      {
        "name": "location",
        "type": "text",
        "label": "Location",
        "required": true,
        "filterable": true,
        "searchable": true
      },
      {
        "name": "job_type",
        "type": "select",
        "label": "Job Type",
        "options": ["full-time", "part-time", "contract", "freelance"],
        "filterable": true,
        "required": true
      },
      {
        "name": "salary_min",
        "type": "number",
        "label": "Minimum Salary",
        "filterable": true,
        "sortable": true
      },
      {
        "name": "salary_max",
        "type": "number",
        "label": "Maximum Salary",
        "filterable": true,
        "sortable": true
      },
      {
        "name": "description",
        "type": "wysiwyg",
        "label": "Job Description",
        "required": true,
        "searchable": true
      },
      {
        "name": "requirements",
        "type": "wysiwyg",
        "label": "Requirements",
        "searchable": true
      },
      {
        "name": "benefits",
        "type": "textarea",
        "label": "Benefits",
        "rows": 5
      },
      {
        "name": "remote_work",
        "type": "boolean",
        "label": "Remote Work Available",
        "filterable": true,
        "default_value": false
      },
      {
        "name": "application_deadline",
        "type": "date",
        "label": "Application Deadline",
        "filterable": true
      },
      {
        "name": "featured",
        "type": "boolean",
        "label": "Featured Job",
        "default_value": false
      }
    ]
  }
}
```

### Product Comparison Content Types
```json
{
  "products": {
    "singular": "product",
    "plural": "products",
    "slug_pattern": "{title}-{brand}",
    "url_structure": "/products/{slug}.html",
    "fields": [
      {
        "name": "title",
        "type": "text",
        "label": "Product Name",
        "required": true,
        "searchable": true
      },
      {
        "name": "brand",
        "type": "text",
        "label": "Brand",
        "required": true,
        "filterable": true,
        "searchable": true
      },
      {
        "name": "price",
        "type": "number",
        "label": "Price",
        "filterable": true,
        "sortable": true,
        "step": 0.01
      },
      {
        "name": "rating",
        "type": "number",
        "label": "Rating",
        "min": 1,
        "max": 5,
        "step": 0.1,
        "filterable": true,
        "sortable": true
      },
      {
        "name": "images",
        "type": "gallery",
        "label": "Product Images",
        "max_images": 10
      },
      {
        "name": "features",
        "type": "repeater",
        "label": "Key Features",
        "sub_fields": [
          {"name": "feature", "type": "text", "required": true}
        ]
      },
      {
        "name": "specifications",
        "type": "repeater",
        "label": "Specifications",
        "sub_fields": [
          {"name": "spec_name", "type": "text", "required": true},
          {"name": "spec_value", "type": "text", "required": true}
        ]
      }
    ]
  }
}
```

## Field Validation System

### Validation Rules
```json
{
  "validation_types": {
    "required": "Field must have a value",
    "min_length": "Minimum character count",
    "max_length": "Maximum character count",
    "pattern": "Regular expression validation",
    "min": "Minimum numeric value",
    "max": "Maximum numeric value",
    "file_size": "Maximum file size in bytes",
    "file_types": "Allowed file extensions",
    "email": "Valid email format",
    "url": "Valid URL format",
    "date": "Valid date format"
  }
}
```

### Custom Validation
```json
{
  "custom_validators": {
    "slug_unique": "Ensure slug is unique across content type",
    "future_date": "Date must be in the future",
    "positive_number": "Number must be positive",
    "valid_phone": "Valid phone number format"
  }
}
```

## Content Relationships

### Taxonomy Relationships
```json
{
  "relationships": {
    "categories": {
      "type": "taxonomy",
      "multiple": true,
      "hierarchical": true
    },
    "tags": {
      "type": "taxonomy",
      "multiple": true,
      "hierarchical": false
    }
  }
}
```

### Content-to-Content Relationships
```json
{
  "relationships": {
    "related_jobs": {
      "type": "content",
      "content_type": "jobs",
      "multiple": true,
      "max_items": 5
    },
    "company": {
      "type": "content",
      "content_type": "companies",
      "multiple": false,
      "required": true
    }
  }
}
```

## Search and Filter Configuration

### Search Settings
```json
{
  "search": {
    "enabled": true,
    "fields": ["title", "description", "company", "location"],
    "weights": {
      "title": 3,
      "company": 2,
      "description": 1,
      "location": 2
    },
    "fuzzy_matching": true,
    "min_query_length": 3
  }
}
```

### Filter Configuration
```json
{
  "filters": {
    "location": {
      "type": "select",
      "multiple": true,
      "source": "field_values"
    },
    "job_type": {
      "type": "checkbox",
      "multiple": true,
      "source": "field_options"
    },
    "salary_range": {
      "type": "range",
      "min_field": "salary_min",
      "max_field": "salary_max"
    },
    "categories": {
      "type": "hierarchy",
      "source": "taxonomy"
    }
  }
}
```

## Content Generation

### Auto-Generated Fields
```json
{
  "auto_fields": {
    "slug": {
      "source": "title",
      "transform": "slugify",
      "unique": true
    },
    "excerpt": {
      "source": "description",
      "transform": "truncate",
      "length": 160
    },
    "reading_time": {
      "source": "description",
      "transform": "calculate_reading_time"
    }
  }
}
```

### Default Values
```json
{
  "defaults": {
    "status": "draft",
    "featured": false,
    "allow_comments": true,
    "created_date": "current_timestamp",
    "modified_date": "current_timestamp"
  }
}
```
