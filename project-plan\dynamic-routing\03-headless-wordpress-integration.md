# Headless WordPress Integration

## Headless Architecture Overview

### Separation of Concerns
```
WordPress (manager/) ←→ Static Site (root/)
        ↓                      ↓
   Content Management    →   Content Delivery
   Admin Interface       →   User Experience
   REST API             →   Static Files + Dynamic Routes
   Database             →   JSON APIs + CDN
```

### WordPress as Content Management System

```php
// manager/wp-content/themes/universal-theme/functions.php
class HeadlessWordPressSetup 
{
    public function __construct() 
    {
        add_action('init', [$this, 'setupHeadlessFeatures']);
        add_action('rest_api_init', [$this, 'customizeRestAPI']);
        add_action('admin_init', [$this, 'setupAdminInterface']);
        add_filter('show_admin_bar', '__return_false'); // Hide admin bar on frontend
    }
    
    public function setupHeadlessFeatures(): void 
    {
        // Disable WordPress frontend (optional)
        if (!is_admin() && !defined('WP_CLI')) {
            add_action('template_redirect', [$this, 'disableFrontend']);
        }
        
        // Enable REST API for all post types
        add_post_type_support('job', 'rest-api');
        add_post_type_support('company', 'rest-api');
        
        // Add CORS headers for API access
        add_action('rest_api_init', [$this, 'addCorsHeaders']);
        
        // Custom post types for job portal
        $this->registerCustomPostTypes();
        
        // Custom fields and meta boxes
        $this->setupCustomFields();
    }
    
    public function disableFrontend(): void 
    {
        // Redirect all frontend requests to static site
        $staticSiteUrl = $this->getStaticSiteUrl();
        
        if ($staticSiteUrl && !is_admin()) {
            wp_redirect($staticSiteUrl . $_SERVER['REQUEST_URI'], 301);
            exit;
        }
    }
    
    public function addCorsHeaders(): void 
    {
        $allowed_origins = [
            $this->getStaticSiteUrl(),
            'http://localhost:3000', // Development
            'https://staging.jobhub.com' // Staging
        ];
        
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        
        if (in_array($origin, $allowed_origins)) {
            header("Access-Control-Allow-Origin: {$origin}");
            header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, Authorization, X-WP-Nonce');
            header('Access-Control-Allow-Credentials: true');
        }
    }
    
    public function registerCustomPostTypes(): void 
    {
        // Job post type
        register_post_type('job', [
            'labels' => [
                'name' => 'Jobs',
                'singular_name' => 'Job',
                'add_new' => 'Add New Job',
                'add_new_item' => 'Add New Job',
                'edit_item' => 'Edit Job',
                'new_item' => 'New Job',
                'view_item' => 'View Job',
                'search_items' => 'Search Jobs',
                'not_found' => 'No jobs found',
                'not_found_in_trash' => 'No jobs found in trash'
            ],
            'public' => true,
            'show_in_rest' => true,
            'rest_base' => 'jobs',
            'supports' => ['title', 'editor', 'thumbnail', 'custom-fields', 'revisions'],
            'menu_icon' => 'dashicons-businessman',
            'has_archive' => false, // Handled by static site
            'rewrite' => false, // No WordPress URLs needed
            'capability_type' => 'post',
            'show_in_menu' => true
        ]);
        
        // Company post type
        register_post_type('company', [
            'labels' => [
                'name' => 'Companies',
                'singular_name' => 'Company',
                'add_new' => 'Add New Company',
                'add_new_item' => 'Add New Company',
                'edit_item' => 'Edit Company',
                'new_item' => 'New Company',
                'view_item' => 'View Company',
                'search_items' => 'Search Companies',
                'not_found' => 'No companies found',
                'not_found_in_trash' => 'No companies found in trash'
            ],
            'public' => true,
            'show_in_rest' => true,
            'rest_base' => 'companies',
            'supports' => ['title', 'editor', 'thumbnail', 'custom-fields'],
            'menu_icon' => 'dashicons-building',
            'has_archive' => false,
            'rewrite' => false,
            'capability_type' => 'post'
        ]);
        
        // Job categories
        register_taxonomy('job_category', 'job', [
            'labels' => [
                'name' => 'Job Categories',
                'singular_name' => 'Job Category',
                'add_new_item' => 'Add New Category',
                'edit_item' => 'Edit Category'
            ],
            'hierarchical' => true,
            'show_in_rest' => true,
            'rest_base' => 'job-categories',
            'rewrite' => false
        ]);
        
        // Job locations
        register_taxonomy('job_location', 'job', [
            'labels' => [
                'name' => 'Job Locations',
                'singular_name' => 'Job Location',
                'add_new_item' => 'Add New Location',
                'edit_item' => 'Edit Location'
            ],
            'hierarchical' => true,
            'show_in_rest' => true,
            'rest_base' => 'job-locations',
            'rewrite' => false
        ]);
    }
    
    public function setupCustomFields(): void 
    {
        // Add meta boxes for job fields
        add_action('add_meta_boxes', [$this, 'addJobMetaBoxes']);
        add_action('save_post', [$this, 'saveJobMetaBoxes']);
        
        // Add meta boxes for company fields
        add_action('add_meta_boxes', [$this, 'addCompanyMetaBoxes']);
        add_action('save_post', [$this, 'saveCompanyMetaBoxes']);
    }
    
    public function addJobMetaBoxes(): void 
    {
        add_meta_box(
            'job_details',
            'Job Details',
            [$this, 'renderJobDetailsMetaBox'],
            'job',
            'normal',
            'high'
        );
    }
    
    public function renderJobDetailsMetaBox(WP_Post $post): void 
    {
        wp_nonce_field('job_details_nonce', 'job_details_nonce');
        
        $company = get_post_meta($post->ID, 'job_company', true);
        $location = get_post_meta($post->ID, 'job_location_name', true);
        $locationSlug = get_post_meta($post->ID, 'job_location_slug', true);
        $salary = get_post_meta($post->ID, 'job_salary', true);
        $type = get_post_meta($post->ID, 'job_type', true);
        $skills = get_post_meta($post->ID, 'job_skills', true) ?: [];
        $applyUrl = get_post_meta($post->ID, 'job_apply_url', true);
        $featured = get_post_meta($post->ID, 'job_featured', true);
        
        ?>
        <table class="form-table">
            <tr>
                <th><label for="job_company">Company</label></th>
                <td>
                    <input type="text" id="job_company" name="job_company" 
                           value="<?= esc_attr($company); ?>" class="regular-text" required />
                </td>
            </tr>
            <tr>
                <th><label for="job_location_name">Location</label></th>
                <td>
                    <input type="text" id="job_location_name" name="job_location_name" 
                           value="<?= esc_attr($location); ?>" class="regular-text" required />
                    <p class="description">e.g., London, UK</p>
                </td>
            </tr>
            <tr>
                <th><label for="job_location_slug">Location Slug</label></th>
                <td>
                    <input type="text" id="job_location_slug" name="job_location_slug" 
                           value="<?= esc_attr($locationSlug); ?>" class="regular-text" required />
                    <p class="description">e.g., london (used in URLs)</p>
                </td>
            </tr>
            <tr>
                <th><label for="job_salary">Salary</label></th>
                <td>
                    <input type="text" id="job_salary" name="job_salary" 
                           value="<?= esc_attr($salary); ?>" class="regular-text" />
                    <p class="description">e.g., £40,000 - £60,000</p>
                </td>
            </tr>
            <tr>
                <th><label for="job_type">Job Type</label></th>
                <td>
                    <select id="job_type" name="job_type" required>
                        <option value="">Select Type</option>
                        <option value="full-time" <?= selected($type, 'full-time'); ?>>Full-time</option>
                        <option value="part-time" <?= selected($type, 'part-time'); ?>>Part-time</option>
                        <option value="contract" <?= selected($type, 'contract'); ?>>Contract</option>
                        <option value="freelance" <?= selected($type, 'freelance'); ?>>Freelance</option>
                        <option value="internship" <?= selected($type, 'internship'); ?>>Internship</option>
                    </select>
                </td>
            </tr>
            <tr>
                <th><label for="job_skills">Skills</label></th>
                <td>
                    <input type="text" id="job_skills" name="job_skills" 
                           value="<?= esc_attr(implode(', ', $skills)); ?>" class="regular-text" />
                    <p class="description">Comma-separated list: React, Node.js, TypeScript</p>
                </td>
            </tr>
            <tr>
                <th><label for="job_apply_url">Apply URL</label></th>
                <td>
                    <input type="url" id="job_apply_url" name="job_apply_url" 
                           value="<?= esc_attr($applyUrl); ?>" class="regular-text" />
                    <p class="description">External application URL</p>
                </td>
            </tr>
            <tr>
                <th><label for="job_featured">Featured Job</label></th>
                <td>
                    <label>
                        <input type="checkbox" id="job_featured" name="job_featured" value="1" 
                               <?= checked($featured, '1'); ?> />
                        Mark as featured job
                    </label>
                </td>
            </tr>
        </table>
        
        <script>
        // Auto-generate location slug
        document.getElementById('job_location_name').addEventListener('input', function() {
            const slug = this.value.toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('job_location_slug').value = slug;
        });
        </script>
        <?php
    }
    
    public function saveJobMetaBoxes(int $postId): void 
    {
        if (!isset($_POST['job_details_nonce']) || 
            !wp_verify_nonce($_POST['job_details_nonce'], 'job_details_nonce')) {
            return;
        }
        
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        if (!current_user_can('edit_post', $postId)) {
            return;
        }
        
        // Save job meta fields
        $fields = [
            'job_company',
            'job_location_name',
            'job_location_slug',
            'job_salary',
            'job_type',
            'job_apply_url'
        ];
        
        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($postId, $field, sanitize_text_field($_POST[$field]));
            }
        }
        
        // Handle skills array
        if (isset($_POST['job_skills'])) {
            $skills = array_map('trim', explode(',', $_POST['job_skills']));
            $skills = array_filter($skills); // Remove empty values
            update_post_meta($postId, 'job_skills', $skills);
        }
        
        // Handle featured checkbox
        $featured = isset($_POST['job_featured']) ? '1' : '0';
        update_post_meta($postId, 'job_featured', $featured);
        
        // Generate company slug
        if (isset($_POST['job_company'])) {
            $companySlug = sanitize_title($_POST['job_company']);
            update_post_meta($postId, 'job_company_slug', $companySlug);
        }
        
        // Trigger static site regeneration
        $this->triggerStaticRegeneration($postId);
    }
    
    private function triggerStaticRegeneration(int $postId): void 
    {
        $location = get_post_meta($postId, 'job_location_slug', true);
        $jobTitle = $this->extractJobTitleSlug(get_the_title($postId));
        
        // Queue regeneration job
        wp_schedule_single_event(time() + 30, 'regenerate_static_site', [
            'type' => 'job_updated',
            'post_id' => $postId,
            'location' => $location,
            'job_title' => $jobTitle
        ]);
    }
    
    private function extractJobTitleSlug(string $title): string 
    {
        // Extract job title from full title
        // e.g., "Senior Developer at Google" -> "senior-developer"
        $title = strtolower($title);
        $title = preg_replace('/\s+at\s+.*$/', '', $title); // Remove "at Company"
        $title = preg_replace('/\s*-\s*.*$/', '', $title);   // Remove "- Company"
        
        return sanitize_title($title);
    }
    
    private function getStaticSiteUrl(): string 
    {
        return get_option('static_site_url', '');
    }
}

new HeadlessWordPressSetup();
```

## Static Site Regeneration System

### Automated Build Triggers
```php
// manager/wp-content/themes/universal-theme/includes/static-regeneration.php
class StaticSiteRegenerator 
{
    private string $buildScriptPath;
    private string $staticSiteRoot;
    
    public function __construct() 
    {
        $this->buildScriptPath = ABSPATH . '../scripts/';
        $this->staticSiteRoot = ABSPATH . '../';
        
        add_action('regenerate_static_site', [$this, 'handleRegeneration']);
        add_action('save_post', [$this, 'scheduleRegeneration'], 10, 2);
        add_action('delete_post', [$this, 'scheduleRegeneration'], 10, 2);
        add_action('updated_option', [$this, 'handleOptionUpdate'], 10, 3);
    }
    
    public function scheduleRegeneration(int $postId, WP_Post $post): void 
    {
        if (!in_array($post->post_type, ['job', 'company'])) {
            return;
        }
        
        if ($post->post_status !== 'publish') {
            return;
        }
        
        $regenerationData = [
            'type' => $post->post_type . '_updated',
            'post_id' => $postId,
            'timestamp' => time()
        ];
        
        if ($post->post_type === 'job') {
            $regenerationData['location'] = get_post_meta($postId, 'job_location_slug', true);
            $regenerationData['job_title'] = $this->extractJobTitleSlug($post->post_title);
        }
        
        // Schedule regeneration with delay to batch multiple changes
        wp_clear_scheduled_hook('regenerate_static_site', [$regenerationData]);
        wp_schedule_single_event(time() + 60, 'regenerate_static_site', [$regenerationData]);
    }
    
    public function handleRegeneration(array $data): void 
    {
        $logFile = $this->staticSiteRoot . 'logs/regeneration.log';
        $this->log("Starting regeneration: " . json_encode($data), $logFile);
        
        try {
            switch ($data['type']) {
                case 'job_updated':
                    $this->regenerateJobPages($data);
                    break;
                    
                case 'company_updated':
                    $this->regenerateCompanyPages($data);
                    break;
                    
                case 'theme_settings_updated':
                    $this->regenerateAllPages();
                    break;
                    
                case 'full_regeneration':
                    $this->regenerateFullSite();
                    break;
            }
            
            $this->log("Regeneration completed successfully", $logFile);
            
        } catch (Exception $e) {
            $this->log("Regeneration failed: " . $e->getMessage(), $logFile);
        }
    }
    
    private function regenerateJobPages(array $data): void 
    {
        $location = $data['location'] ?? '';
        $jobTitle = $data['job_title'] ?? '';
        
        // Regenerate location-specific pages
        if ($location) {
            $this->runBuildScript('build-location-pages.js', [
                'location' => $location
            ]);
        }
        
        // Regenerate job title specific pages
        if ($location && $jobTitle) {
            $this->runBuildScript('build-job-title-pages.js', [
                'location' => $location,
                'job_title' => $jobTitle
            ]);
        }
        
        // Update API endpoints
        $this->runBuildScript('build-api-endpoints.js', [
            'incremental' => true,
            'location' => $location,
            'job_title' => $jobTitle
        ]);
        
        // Update search index
        $this->runBuildScript('update-search-index.js', [
            'post_id' => $data['post_id']
        ]);
    }
    
    private function regenerateCompanyPages(array $data): void 
    {
        $this->runBuildScript('build-company-pages.js', [
            'company_id' => $data['post_id']
        ]);
        
        $this->runBuildScript('build-api-endpoints.js', [
            'incremental' => true,
            'type' => 'company'
        ]);
    }
    
    private function regenerateAllPages(): void 
    {
        // Regenerate components
        $this->runBuildScript('build-components.js');
        
        // Regenerate all static pages
        $this->runBuildScript('build-static-pages.js');
        
        // Regenerate CSS
        $this->runBuildScript('build-css.js');
    }
    
    private function regenerateFullSite(): void 
    {
        $this->runBuildScript('build-full-site.js');
    }
    
    private function runBuildScript(string $script, array $args = []): void 
    {
        $scriptPath = $this->buildScriptPath . $script;
        
        if (!file_exists($scriptPath)) {
            throw new Exception("Build script not found: {$script}");
        }
        
        $command = "cd {$this->staticSiteRoot} && node {$scriptPath}";
        
        if (!empty($args)) {
            $command .= " '" . json_encode($args) . "'";
        }
        
        // Run in background
        $command .= " > logs/build.log 2>&1 &";
        
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new Exception("Build script failed with code: {$returnCode}");
        }
    }
    
    private function log(string $message, string $logFile): void 
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}\n";
        
        $logDir = dirname($logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    private function extractJobTitleSlug(string $title): string 
    {
        $title = strtolower($title);
        $title = preg_replace('/\s+at\s+.*$/', '', $title);
        $title = preg_replace('/\s*-\s*.*$/', '', $title);
        
        return sanitize_title($title);
    }
}

new StaticSiteRegenerator();
```

## WordPress Admin Enhancements

### Custom Admin Dashboard
```php
// manager/wp-content/themes/universal-theme/includes/admin-dashboard.php
class HeadlessAdminDashboard 
{
    public function __construct() 
    {
        add_action('wp_dashboard_setup', [$this, 'addDashboardWidgets']);
        add_action('admin_menu', [$this, 'addStaticSiteMenu']);
        add_action('admin_enqueue_scripts', [$this, 'enqueueAdminAssets']);
    }
    
    public function addDashboardWidgets(): void 
    {
        wp_add_dashboard_widget(
            'static_site_status',
            'Static Site Status',
            [$this, 'renderStaticSiteStatusWidget']
        );
        
        wp_add_dashboard_widget(
            'job_statistics',
            'Job Statistics',
            [$this, 'renderJobStatisticsWidget']
        );
        
        wp_add_dashboard_widget(
            'recent_regenerations',
            'Recent Regenerations',
            [$this, 'renderRecentRegenerationsWidget']
        );
    }
    
    public function renderStaticSiteStatusWidget(): void 
    {
        $staticSiteUrl = get_option('static_site_url', '');
        $lastRegeneration = get_option('last_static_regeneration', '');
        $buildStatus = $this->getLastBuildStatus();
        
        ?>
        <div class="static-site-status">
            <p><strong>Static Site URL:</strong> 
                <?php if ($staticSiteUrl): ?>
                    <a href="<?= esc_url($staticSiteUrl); ?>" target="_blank"><?= esc_html($staticSiteUrl); ?></a>
                <?php else: ?>
                    <span style="color: #d63638;">Not configured</span>
                <?php endif; ?>
            </p>
            
            <p><strong>Last Regeneration:</strong> 
                <?= $lastRegeneration ? esc_html(date('Y-m-d H:i:s', $lastRegeneration)) : 'Never'; ?>
            </p>
            
            <p><strong>Build Status:</strong> 
                <span class="build-status build-status--<?= esc_attr($buildStatus['status']); ?>">
                    <?= esc_html(ucfirst($buildStatus['status'])); ?>
                </span>
            </p>
            
            <div class="static-site-actions">
                <button type="button" class="button button-primary" id="trigger-full-regeneration">
                    Regenerate Full Site
                </button>
                <button type="button" class="button" id="trigger-incremental-regeneration">
                    Incremental Update
                </button>
            </div>
        </div>
        
        <style>
        .build-status {
            padding: 2px 8px;
            border-radius: 3px;
            font-weight: 500;
        }
        .build-status--success { background: #d1e7dd; color: #0f5132; }
        .build-status--error { background: #f8d7da; color: #721c24; }
        .build-status--building { background: #fff3cd; color: #856404; }
        .static-site-actions { margin-top: 15px; }
        .static-site-actions .button { margin-right: 10px; }
        </style>
        
        <script>
        document.getElementById('trigger-full-regeneration').addEventListener('click', function() {
            if (confirm('This will regenerate the entire static site. Continue?')) {
                fetch(ajaxurl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'action=trigger_static_regeneration&type=full&_wpnonce=' + '<?= wp_create_nonce('static_regeneration'); ?>'
                }).then(response => {
                    if (response.ok) {
                        alert('Full regeneration triggered successfully!');
                        location.reload();
                    } else {
                        alert('Failed to trigger regeneration.');
                    }
                });
            }
        });
        </script>
        <?php
    }
    
    public function renderJobStatisticsWidget(): void 
    {
        $totalJobs = wp_count_posts('job')->publish;
        $totalCompanies = wp_count_posts('company')->publish;
        $featuredJobs = $this->getFeaturedJobsCount();
        $recentJobs = $this->getRecentJobsCount();
        
        ?>
        <div class="job-statistics">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?= esc_html($totalJobs); ?></div>
                    <div class="stat-label">Total Jobs</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= esc_html($totalCompanies); ?></div>
                    <div class="stat-label">Companies</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= esc_html($featuredJobs); ?></div>
                    <div class="stat-label">Featured Jobs</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= esc_html($recentJobs); ?></div>
                    <div class="stat-label">This Week</div>
                </div>
            </div>
        </div>
        
        <style>
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2271b1;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        </style>
        <?php
    }
    
    private function getLastBuildStatus(): array 
    {
        $logFile = ABSPATH . '../logs/build.log';
        
        if (!file_exists($logFile)) {
            return ['status' => 'unknown', 'message' => 'No build log found'];
        }
        
        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $lastLine = end($lines);
        
        if (strpos($lastLine, 'success') !== false) {
            return ['status' => 'success', 'message' => 'Last build successful'];
        } elseif (strpos($lastLine, 'error') !== false || strpos($lastLine, 'failed') !== false) {
            return ['status' => 'error', 'message' => 'Last build failed'];
        } else {
            return ['status' => 'building', 'message' => 'Build in progress'];
        }
    }
    
    private function getFeaturedJobsCount(): int 
    {
        global $wpdb;
        
        return (int) $wpdb->get_var("
            SELECT COUNT(*) 
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'job' 
            AND p.post_status = 'publish'
            AND pm.meta_key = 'job_featured'
            AND pm.meta_value = '1'
        ");
    }
    
    private function getRecentJobsCount(): int 
    {
        global $wpdb;
        
        $weekAgo = date('Y-m-d H:i:s', strtotime('-1 week'));
        
        return (int) $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*) 
            FROM {$wpdb->posts}
            WHERE post_type = 'job' 
            AND post_status = 'publish'
            AND post_date >= %s
        ", $weekAgo));
    }
}

new HeadlessAdminDashboard();
```

This headless WordPress integration provides:

1. **Complete Content Management** - Full WordPress admin for content creation
2. **Custom Post Types** - Jobs, companies, categories with proper fields
3. **REST API Integration** - Seamless data flow to static site
4. **Automatic Regeneration** - Triggers static site updates on content changes
5. **Admin Dashboard** - Custom widgets showing static site status
6. **CORS Support** - Proper headers for API access
7. **Build Monitoring** - Track regeneration status and logs

The system maintains WordPress as a powerful CMS while delivering content through a lightning-fast static site! 🚀
