# Advanced Asset Optimization Strategy

## Single Bundle Optimization

### CSS Optimization Pipeline
```
Source CSS Files → PostCSS → PurgeCSS → CSSNano → Gzip → Single Bundle
     ↓              ↓         ↓          ↓        ↓         ↓
Multiple Files → Processing → Cleanup → Minify → Compress → site.min.css
```

### Build Process Configuration
```javascript
// webpack.config.js - Advanced CSS optimization
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const PurgeCSSPlugin = require('purgecss-webpack-plugin');

module.exports = {
  optimization: {
    minimizer: [
      new CssMinimizerPlugin({
        minimizerOptions: {
          preset: [
            'advanced',
            {
              discardComments: { removeAll: true },
              normalizeWhitespace: true,
              colormin: true,
              convertValues: true,
              discardDuplicates: true,
              mergeLonghand: true,
              mergeRules: true,
              minifyFontValues: true,
              minifyParams: true,
              minifySelectors: true,
              normalizeCharset: true,
              normalizeDisplayValues: true,
              normalizePositions: true,
              normalizeRepeatStyle: true,
              normalizeString: true,
              normalizeTimingFunctions: true,
              normalizeUnicode: true,
              normalizeUrl: true,
              orderedValues: true,
              reduceIdents: true,
              reduceInitial: true,
              reduceTransforms: true,
              svgo: true,
              uniqueSelectors: true
            }
          ]
        }
      })
    ]
  },
  
  plugins: [
    new MiniCssExtractPlugin({
      filename: 'assets/css/site.min.css'
    }),
    
    new PurgeCSSPlugin({
      paths: [
        './dist/**/*.html',
        './src/js/**/*.js',
        './components/**/*.html'
      ],
      safelist: [
        /^js-/,
        /^data-/,
        'active',
        'open',
        'visible',
        'loading',
        'error',
        'success',
        /^swiper/,
        /^aos/
      ]
    })
  ]
};
```

## JavaScript Optimization

### Bundle Strategy
```javascript
// Single optimized JavaScript bundle
const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  entry: {
    main: './src/js/main.js'
  },
  
  output: {
    filename: 'assets/js/[name].min.js',
    chunkFilename: 'assets/js/[name].[contenthash].chunk.js'
  },
  
  optimization: {
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
            pure_funcs: ['console.log', 'console.info', 'console.debug'],
            passes: 2,
            unsafe_arrows: true,
            unsafe_methods: true,
            unsafe_proto: true,
            keep_infinity: true,
            reduce_vars: true,
            collapse_vars: true,
            hoist_funs: true,
            hoist_vars: true,
            if_return: true,
            join_vars: true,
            cascade: true,
            side_effects: false
          },
          mangle: {
            safari10: true,
            properties: {
              regex: /^_/
            }
          },
          format: {
            comments: false,
            ascii_only: true
          }
        },
        extractComments: false
      })
    ],
    
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendor',
          chunks: 'all',
          priority: 10
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          priority: 5,
          enforce: true
        }
      }
    }
  }
};
```

## Critical CSS Strategy

### Inline Critical CSS
```php
// WordPress service to generate critical CSS
class CriticalCSSService 
{
    public function generateCriticalCSS(): string 
    {
        $criticalStyles = [
            $this->getResetStyles(),
            $this->getTypographyStyles(),
            $this->getHeaderStyles(),
            $this->getAboveFoldStyles()
        ];
        
        $criticalCSS = implode('', $criticalStyles);
        
        // Minify critical CSS
        return $this->minifyCSS($criticalCSS);
    }
    
    private function getAboveFoldStyles(): string 
    {
        return '
        .header{background:#fff;border-bottom:1px solid #e5e7eb;position:sticky;top:0;z-index:100}
        .header__container{display:flex;align-items:center;justify-content:space-between;max-width:1200px;margin:0 auto;padding:1rem}
        .hero{min-height:60vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%)}
        .hero__title{font-size:clamp(2rem,5vw,4rem);font-weight:700;color:#fff;text-align:center}
        ';
    }
    
    private function minifyCSS(string $css): string 
    {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Remove whitespace
        $css = str_replace(["\r\n", "\r", "\n", "\t"], '', $css);
        $css = preg_replace('/\s+/', ' ', $css);
        $css = preg_replace('/\s*([{}:;,>+~])\s*/', '$1', $css);
        
        return trim($css);
    }
}
```

## Image Optimization

### Advanced Image Processing
```php
// Image optimization service
class ImageOptimizationService 
{
    private array $formats = ['avif', 'webp', 'jpg'];
    private array $breakpoints = [320, 640, 768, 1024, 1280, 1920];
    
    public function optimizeImage(string $imagePath): array 
    {
        $optimizedImages = [];
        
        foreach ($this->breakpoints as $width) {
            foreach ($this->formats as $format) {
                $optimizedPath = $this->generateResponsiveImage(
                    $imagePath, 
                    $width, 
                    $format
                );
                
                $optimizedImages[$format][$width] = $optimizedPath;
            }
        }
        
        return $optimizedImages;
    }
    
    private function generateResponsiveImage(string $input, int $width, string $format): string 
    {
        $filename = pathinfo($input, PATHINFO_FILENAME);
        $outputPath = "assets/images/optimized/{$filename}-{$width}w.{$format}";
        
        $image = imagecreatefromstring(file_get_contents($input));
        
        // Resize image
        $resized = imagescale($image, $width, -1, IMG_BICUBIC);
        
        // Save in optimized format
        switch ($format) {
            case 'avif':
                imageavif($resized, $outputPath, 75);
                break;
            case 'webp':
                imagewebp($resized, $outputPath, 85);
                break;
            case 'jpg':
                imagejpeg($resized, $outputPath, 85);
                break;
        }
        
        imagedestroy($image);
        imagedestroy($resized);
        
        return $outputPath;
    }
    
    public function generatePictureElement(string $imageName, string $alt): string 
    {
        return "
        <picture>
            <source media='(min-width: 1280px)' 
                    srcset='{$imageName}-1920w.avif 1920w, {$imageName}-1280w.avif 1280w' 
                    type='image/avif'>
            <source media='(min-width: 1024px)' 
                    srcset='{$imageName}-1280w.webp 1280w, {$imageName}-1024w.webp 1024w' 
                    type='image/webp'>
            <source media='(min-width: 768px)' 
                    srcset='{$imageName}-1024w.webp 1024w, {$imageName}-768w.webp 768w' 
                    type='image/webp'>
            <source srcset='{$imageName}-768w.webp 768w, {$imageName}-640w.webp 640w, {$imageName}-320w.webp 320w' 
                    type='image/webp'>
            <img src='{$imageName}-768w.jpg' 
                 srcset='{$imageName}-320w.jpg 320w, {$imageName}-640w.jpg 640w, {$imageName}-768w.jpg 768w' 
                 sizes='(min-width: 1024px) 1024px, (min-width: 768px) 768px, 100vw' 
                 alt='{$alt}' 
                 loading='lazy' 
                 decoding='async'>
        </picture>";
    }
}
```

## Compression and Caching

### Gzip/Brotli Compression
```php
// Asset compression service
class CompressionService 
{
    public function compressAssets(): void 
    {
        $assets = [
            'assets/css/site.min.css',
            'assets/js/main.min.js',
            'assets/js/vendor.min.js'
        ];
        
        foreach ($assets as $asset) {
            // Gzip compression
            $this->createGzipVersion($asset);
            
            // Brotli compression (better compression)
            $this->createBrotliVersion($asset);
        }
    }
    
    private function createGzipVersion(string $filePath): void 
    {
        $content = file_get_contents($filePath);
        $compressed = gzencode($content, 9); // Maximum compression
        file_put_contents($filePath . '.gz', $compressed);
    }
    
    private function createBrotliVersion(string $filePath): void 
    {
        // Using brotli command line tool
        exec("brotli -q 11 -o {$filePath}.br {$filePath}");
    }
}
```

## Performance Monitoring

### Asset Size Tracking
```php
// Performance budget enforcement
class PerformanceBudget 
{
    private array $budgets = [
        'critical_css' => 14 * 1024,      // 14KB
        'main_css' => 50 * 1024,          // 50KB
        'main_js' => 100 * 1024,          // 100KB
        'vendor_js' => 150 * 1024,        // 150KB
        'total_assets' => 500 * 1024      // 500KB
    ];
    
    public function checkBudgets(): array 
    {
        $results = [];
        
        foreach ($this->budgets as $asset => $budget) {
            $actualSize = $this->getAssetSize($asset);
            $results[$asset] = [
                'budget' => $budget,
                'actual' => $actualSize,
                'passed' => $actualSize <= $budget,
                'percentage' => round(($actualSize / $budget) * 100, 2)
            ];
        }
        
        return $results;
    }
    
    private function getAssetSize(string $asset): int 
    {
        $filePaths = [
            'critical_css' => 'assets/css/critical.css',
            'main_css' => 'assets/css/site.min.css',
            'main_js' => 'assets/js/main.min.js',
            'vendor_js' => 'assets/js/vendor.min.js'
        ];
        
        if (isset($filePaths[$asset])) {
            return filesize($filePaths[$asset]) ?: 0;
        }
        
        return 0;
    }
}
```

## Build Optimization Results

### Target Performance Metrics
```
Critical CSS (inlined):     14KB  (loads instantly)
Main CSS Bundle:           45KB  (single request)
Main JS Bundle:            85KB  (single request)
Vendor JS Bundle:         120KB  (cached separately)
Total First Load:         264KB  (under 300KB target)

Compression Results:
- CSS: 45KB → 12KB (gzipped) → 9KB (brotli)
- JS:  85KB → 28KB (gzipped) → 22KB (brotli)
- Total: 130KB → 40KB (gzipped) → 31KB (brotli)
```

### File Structure After Optimization
```
assets/
├── css/
│   ├── site.min.css           (single CSS bundle - 45KB)
│   ├── site.min.css.gz        (gzipped version - 12KB)
│   ├── site.min.css.br        (brotli version - 9KB)
│   └── critical.css           (inlined critical CSS - 14KB)
├── js/
│   ├── main.min.js            (main bundle - 85KB)
│   ├── main.min.js.gz         (gzipped - 28KB)
│   ├── main.min.js.br         (brotli - 22KB)
│   ├── vendor.min.js          (vendor bundle - 120KB)
│   ├── vendor.min.js.gz       (gzipped - 35KB)
│   └── vendor.min.js.br       (brotli - 28KB)
└── images/
    └── optimized/
        ├── logo-320w.avif     (mobile logo)
        ├── logo-640w.webp     (tablet logo)
        └── logo-1024w.jpg     (desktop fallback)
```
