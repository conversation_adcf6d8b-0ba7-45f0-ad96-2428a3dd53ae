# Platform Configuration System

## Configuration Structure
Each platform is defined by a comprehensive JSON configuration file that controls all aspects of the system behavior.

## Base Configuration Schema

### Platform Identity
```json
{
  "platform_type": "job-portal",
  "site_name": "JobHub",
  "base_url": "https://example.com",
  "admin_url": "https://example.com/manager",
  "version": "1.0.0",
  "environment": "production"
}
```

### Content Type Definitions
```json
{
  "content_types": {
    "primary": {
      "name": "jobs",
      "singular": "job",
      "slug_pattern": "{title}-{company}-{location}",
      "url_structure": "/jobs/{slug}.html",
      "archive_url": "/jobs/",
      "pagination": 20,
      "sort_options": ["date", "title", "company", "location"]
    }
  }
}
```

## Platform Type Templates

### Job Portal Configuration
```json
{
  "platform_type": "job-portal",
  "content_types": {
    "primary": {
      "name": "jobs",
      "fields": [
        {"name": "title", "type": "text", "required": true, "searchable": true},
        {"name": "company", "type": "text", "required": true, "searchable": true},
        {"name": "location", "type": "text", "required": true, "filterable": true},
        {"name": "salary", "type": "text", "filterable": true},
        {"name": "job_type", "type": "select", "options": ["full-time", "part-time", "contract"]},
        {"name": "description", "type": "wysiwyg", "searchable": true},
        {"name": "requirements", "type": "wysiwyg"},
        {"name": "benefits", "type": "wysiwyg"},
        {"name": "application_url", "type": "url"},
        {"name": "featured", "type": "boolean", "default": false}
      ]
    },
    "secondary": {
      "name": "companies",
      "fields": [
        {"name": "name", "type": "text", "required": true},
        {"name": "description", "type": "wysiwyg"},
        {"name": "website", "type": "url"},
        {"name": "logo", "type": "image"},
        {"name": "size", "type": "select", "options": ["startup", "small", "medium", "large"]},
        {"name": "industry", "type": "text"}
      ]
    }
  }
}
```

### Comparison Site Configuration
```json
{
  "platform_type": "comparison-site",
  "content_types": {
    "primary": {
      "name": "products",
      "fields": [
        {"name": "title", "type": "text", "required": true},
        {"name": "brand", "type": "text", "required": true},
        {"name": "price", "type": "number", "filterable": true},
        {"name": "rating", "type": "number", "min": 1, "max": 5},
        {"name": "features", "type": "repeater"},
        {"name": "pros", "type": "repeater"},
        {"name": "cons", "type": "repeater"},
        {"name": "affiliate_link", "type": "url"},
        {"name": "images", "type": "gallery"}
      ]
    }
  }
}
```

## Taxonomy Configuration

### Categories System
```json
{
  "taxonomies": {
    "categories": {
      "name": "Categories",
      "slug": "categories",
      "hierarchical": true,
      "url_structure": "/categories/{slug}.html",
      "show_in_menu": true,
      "meta_fields": [
        {"name": "description", "type": "textarea"},
        {"name": "icon", "type": "image"},
        {"name": "color", "type": "color"}
      ]
    }
  }
}
```

### Location-Based Taxonomy
```json
{
  "taxonomies": {
    "locations": {
      "name": "Locations",
      "slug": "locations",
      "hierarchical": true,
      "url_structure": "/locations/{slug}.html",
      "levels": ["country", "state", "city"],
      "auto_populate": true,
      "geo_data": true
    }
  }
}
```

## Layout Configuration

### Component Arrangement
```json
{
  "layout": {
    "template": "two-column",
    "has_sidebar": true,
    "sidebar_position": "right",
    "sidebar_components": [
      {"type": "latest_posts", "count": 5},
      {"type": "categories", "style": "list"},
      {"type": "featured_items", "count": 3},
      {"type": "newsletter_signup"}
    ],
    "header_menu": [
      {"label": "Home", "url": "/"},
      {"label": "Browse", "url": "/jobs/"},
      {"label": "Categories", "url": "/categories/"},
      {"label": "Companies", "url": "/companies/"},
      {"label": "About", "url": "/about/"}
    ]
  }
}
```

### Footer Configuration
```json
{
  "layout": {
    "footer_sections": [
      {
        "title": "Quick Links",
        "type": "links",
        "items": [
          {"label": "Privacy Policy", "url": "/privacy/"},
          {"label": "Terms of Service", "url": "/terms/"},
          {"label": "Contact", "url": "/contact/"}
        ]
      },
      {
        "title": "Contact Info",
        "type": "contact",
        "email": "<EMAIL>",
        "phone": "******-0123"
      },
      {
        "title": "Social Media",
        "type": "social",
        "platforms": ["twitter", "linkedin", "facebook"]
      }
    ]
  }
}
```

## SEO Configuration

### Meta Data Settings
```json
{
  "seo": {
    "site_title": "JobHub - Find Your Dream Job",
    "site_description": "Discover thousands of job opportunities from top companies",
    "default_image": "/assets/images/og-default.jpg",
    "twitter_handle": "@jobhub",
    "facebook_app_id": "123456789",
    "schema_org": {
      "organization": {
        "name": "JobHub",
        "url": "https://example.com",
        "logo": "https://example.com/logo.png"
      }
    }
  }
}
```

## Performance Configuration

### Optimization Settings
```json
{
  "performance": {
    "image_optimization": true,
    "css_minification": true,
    "js_minification": true,
    "gzip_compression": true,
    "cache_duration": 86400,
    "lazy_loading": true,
    "critical_css": true
  }
}
```

## Configuration Validation

### Required Fields
- platform_type
- site_name
- base_url
- content_types (at least one primary)

### Validation Rules
- URL structures must be unique
- Field names must be valid identifiers
- Required fields cannot be empty
- Taxonomy slugs must be unique
- Menu items must have valid URLs

## Configuration Management

### Environment-Specific Configs
- `config.development.json`
- `config.staging.json`
- `config.production.json`

### Configuration Inheritance
- Base configuration with platform-specific overrides
- Environment-specific settings
- Feature flags for gradual rollouts
