# Responsive Design and Skeleton Loading System

## Responsive Design Strategy

### Mobile-First Approach
Design and develop for mobile devices first, then progressively enhance for larger screens using min-width media queries.

### Breakpoint System
```css
:root {
  --breakpoint-xs: 475px;   /* Small phones */
  --breakpoint-sm: 640px;   /* Large phones */
  --breakpoint-md: 768px;   /* Tablets */
  --breakpoint-lg: 1024px;  /* Small laptops */
  --breakpoint-xl: 1280px;  /* Desktops */
  --breakpoint-2xl: 1536px; /* Large desktops */
}
```

### Container Queries
Modern responsive design using container queries for component-level responsiveness.

```css
.job-listings {
  container-type: inline-size;
}

@container (min-width: 400px) {
  .job-card {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 1rem;
  }
}
```

## Responsive Grid System

### Flexible Grid Layout
```css
/* utilities/responsive.css */
.grid-responsive {
  display: grid;
  gap: var(--grid-gap);
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-responsive--small {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.grid-responsive--large {
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* Responsive columns */
.grid-cols-responsive {
  display: grid;
  gap: var(--grid-gap);
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .grid-cols-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .grid-cols-responsive {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-cols-responsive {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

### Layout Containers
```css
.layout-container {
  display: grid;
  gap: 2rem;
  grid-template-areas: 
    "header"
    "main"
    "sidebar"
    "footer";
}

@media (min-width: 768px) {
  .layout-container {
    grid-template-areas: 
      "header header"
      "main sidebar"
      "footer footer";
    grid-template-columns: 1fr 300px;
  }
}

@media (min-width: 1024px) {
  .layout-container {
    grid-template-columns: 1fr 350px;
  }
}

.layout-header { grid-area: header; }
.layout-main { grid-area: main; }
.layout-sidebar { grid-area: sidebar; }
.layout-footer { grid-area: footer; }
```

## Responsive Typography

### Fluid Typography
```css
:root {
  /* Fluid font sizes using clamp() */
  --text-xs: clamp(0.7rem, 0.66rem + 0.2vw, 0.75rem);
  --text-sm: clamp(0.8rem, 0.74rem + 0.3vw, 0.875rem);
  --text-base: clamp(0.9rem, 0.83rem + 0.35vw, 1rem);
  --text-lg: clamp(1rem, 0.91rem + 0.45vw, 1.125rem);
  --text-xl: clamp(1.1rem, 0.98rem + 0.6vw, 1.25rem);
  --text-2xl: clamp(1.25rem, 1.08rem + 0.85vw, 1.5rem);
  --text-3xl: clamp(1.5rem, 1.24rem + 1.3vw, 1.875rem);
  --text-4xl: clamp(1.75rem, 1.39rem + 1.8vw, 2.25rem);
  
  /* Responsive line heights */
  --leading-tight: clamp(1.2, 1.15 + 0.25vw, 1.25);
  --leading-normal: clamp(1.4, 1.35 + 0.25vw, 1.5);
  --leading-relaxed: clamp(1.6, 1.55 + 0.25vw, 1.75);
}

/* Responsive headings */
h1 {
  font-size: var(--text-4xl);
  line-height: var(--leading-tight);
}

h2 {
  font-size: var(--text-3xl);
  line-height: var(--leading-tight);
}

h3 {
  font-size: var(--text-2xl);
  line-height: var(--leading-normal);
}

/* Responsive body text */
body {
  font-size: var(--text-base);
  line-height: var(--leading-normal);
}

.text-small {
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
}
```

### Responsive Spacing
```css
/* utilities/spacing.css */
:root {
  /* Fluid spacing using clamp() */
  --space-xs: clamp(0.25rem, 0.2rem + 0.25vw, 0.5rem);
  --space-sm: clamp(0.5rem, 0.4rem + 0.5vw, 0.75rem);
  --space-md: clamp(0.75rem, 0.6rem + 0.75vw, 1rem);
  --space-lg: clamp(1rem, 0.8rem + 1vw, 1.5rem);
  --space-xl: clamp(1.5rem, 1.2rem + 1.5vw, 2rem);
  --space-2xl: clamp(2rem, 1.6rem + 2vw, 3rem);
  --space-3xl: clamp(3rem, 2.4rem + 3vw, 4rem);
}

/* Responsive margin utilities */
.m-responsive { margin: var(--space-md); }
.mt-responsive { margin-top: var(--space-md); }
.mb-responsive { margin-bottom: var(--space-md); }
.mx-responsive { margin-left: var(--space-md); margin-right: var(--space-md); }
.my-responsive { margin-top: var(--space-md); margin-bottom: var(--space-md); }

/* Responsive padding utilities */
.p-responsive { padding: var(--space-md); }
.pt-responsive { padding-top: var(--space-md); }
.pb-responsive { padding-bottom: var(--space-md); }
.px-responsive { padding-left: var(--space-md); padding-right: var(--space-md); }
.py-responsive { padding-top: var(--space-md); padding-bottom: var(--space-md); }

/* Large spacing */
.section-spacing {
  padding-top: var(--space-2xl);
  padding-bottom: var(--space-2xl);
}

.container-spacing {
  padding-left: var(--space-lg);
  padding-right: var(--space-lg);
}
```

## Skeleton Loading System

### Responsive Skeleton Components
```css
/* components/skeleton.css */
.skeleton {
  background: linear-gradient(
    90deg,
    var(--color-gray-200) 25%,
    var(--color-gray-100) 50%,
    var(--color-gray-200) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 0.25rem;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Dark mode skeleton */
@media (prefers-color-scheme: dark) {
  .skeleton {
    background: linear-gradient(
      90deg,
      var(--color-gray-700) 25%,
      var(--color-gray-600) 50%,
      var(--color-gray-700) 75%
    );
  }
}

/* Skeleton shapes */
.skeleton-text {
  height: 1.2em;
  width: 100%;
}

.skeleton-text--title {
  height: 1.5em;
  width: 75%;
  margin-bottom: 0.5rem;
}

.skeleton-text--subtitle {
  height: 1.2em;
  width: 50%;
  margin-bottom: 0.75rem;
}

.skeleton-text--paragraph {
  height: 1.2em;
  margin-bottom: 0.5rem;
}

.skeleton-text--paragraph:last-child {
  width: 60%;
}

.skeleton-circle {
  border-radius: 50%;
  aspect-ratio: 1;
}

.skeleton-rectangle {
  border-radius: 0.5rem;
}

.skeleton-rounded {
  border-radius: 0.75rem;
}
```

### Smart Skeleton CSS with Aspect Ratios
```css
.skeleton-responsive {
  width: 100%;
  aspect-ratio: var(--aspect-ratio, 16/9);
}

.skeleton-responsive[data-aspect="1:1"] {
  aspect-ratio: 1;
}

.skeleton-responsive[data-aspect="4:3"] {
  aspect-ratio: 4/3;
}

.skeleton-responsive[data-aspect="16:9"] {
  aspect-ratio: 16/9;
}

.skeleton-responsive[data-aspect="21:9"] {
  aspect-ratio: 21/9;
}

/* Multi-line text skeleton */
.skeleton-text[data-lines="2"] {
  height: 2.4em; /* 2 lines of text */
}

.skeleton-text[data-lines="3"] {
  height: 3.6em; /* 3 lines of text */
}

.skeleton-text[data-lines="4"] {
  height: 4.8em; /* 4 lines of text */
}
```

### Job Card Skeleton
```html
<!-- Example: Job Card Skeleton -->
<div class="job-card skeleton-card">
  <div class="skeleton skeleton-responsive" data-aspect="16:9"></div>
  <div class="skeleton skeleton-text skeleton-text--title"></div>
  <div class="skeleton skeleton-text skeleton-text--subtitle"></div>
  <div class="skeleton skeleton-text" data-lines="3"></div>
  <div class="skeleton-footer">
    <div class="skeleton skeleton-text" style="width: 30%;"></div>
    <div class="skeleton skeleton-rectangle" style="width: 80px; height: 32px;"></div>
  </div>
</div>
```

### Company Card Skeleton
```css
.skeleton-company-card {
  padding: 1.5rem;
  text-align: center;
}

.skeleton-company-card .skeleton-circle {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem auto;
}

.skeleton-company-card .skeleton-text--name {
  width: 60%;
  margin: 0 auto 0.5rem auto;
}

.skeleton-company-card .skeleton-text--industry {
  width: 40%;
  margin: 0 auto 1rem auto;
}

.skeleton-company-card .skeleton-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 1rem;
  border-top: 1px solid var(--color-border);
}

.skeleton-company-card .skeleton-stat {
  text-align: center;
  width: 60px;
}
```

## Responsive Images

### Responsive Image System
```css
.img-responsive {
  max-width: 100%;
  height: auto;
  display: block;
}

.img-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.img-contain {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Aspect ratio containers */
.aspect-ratio {
  position: relative;
  overflow: hidden;
}

.aspect-ratio::before {
  content: '';
  display: block;
  padding-top: var(--aspect-ratio, 56.25%); /* 16:9 default */
}

.aspect-ratio > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.aspect-ratio--square {
  --aspect-ratio: 100%;
}

.aspect-ratio--4-3 {
  --aspect-ratio: 75%;
}

.aspect-ratio--16-9 {
  --aspect-ratio: 56.25%;
}

.aspect-ratio--21-9 {
  --aspect-ratio: 42.86%;
}
```

### Picture Element for Responsive Images
```html
<picture class="img-responsive">
  <source 
    media="(min-width: 1024px)" 
    srcset="image-large.webp 1x, <EMAIL> 2x"
    type="image/webp">
  <source 
    media="(min-width: 768px)" 
    srcset="image-medium.webp 1x, <EMAIL> 2x"
    type="image/webp">
  <source 
    srcset="image-small.webp 1x, <EMAIL> 2x"
    type="image/webp">
  <img 
    src="image-medium.jpg" 
    srcset="image-small.jpg 480w, image-medium.jpg 768w, image-large.jpg 1024w"
    sizes="(min-width: 1024px) 1024px, (min-width: 768px) 768px, 480px"
    alt="Description"
    loading="lazy">
</picture>
```

## Performance Optimization

### CSS Grid/Flexbox Performance
```css
/* Optimized grid layouts */
.grid-performance {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  /* Use transform instead of changing layout properties */
  will-change: transform;
}

.grid-item {
  /* Promote to composite layer for smooth animations */
  transform: translateZ(0);
  transition: transform 0.2s ease;
}

.grid-item:hover {
  transform: translateY(-4px) translateZ(0);
}
```

### Animation Performance
```css
/* Use transform and opacity for smooth animations */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .fade-in {
    animation: none;
    opacity: 1;
    transform: none;
  }
  
  .skeleton {
    animation: none;
  }
}
```

### Container Query Optimization
```css
/* Efficient container queries */
.responsive-component {
  container-type: inline-size;
  /* Contain layout and style for better performance */
  contain: layout style;
}

@container (min-width: 400px) {
  .responsive-component .content {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1rem;
  }
}

@container (min-width: 600px) {
  .responsive-component .content {
    grid-template-columns: auto 1fr auto;
  }
}
```
