# File Structure and Organization

## Generated Site Structure

### Root Directory Layout
```
/
├── index.html                    (homepage)
├── sitemap.xml                   (SEO sitemap)
├── robots.txt                    (search engine directives)
├── manifest.json                 (PWA manifest)
├── sw.js                         (service worker)
├── 404.html                      (error page)
├── assets/                       (compiled assets)
├── jobs/                         (job pages)
├── companies/                    (company pages)
├── categories/                   (category pages)
├── locations/                    (location pages)
└── components/                   (reusable components)
```

### Content Directory Structure
```
/jobs/
├── index.html                              (jobs archive/listing)
├── page-2.html                             (pagination)
├── page-3.html                             (pagination)
├── site-manager-amazon-new-york.html       (individual job)
├── frontend-developer-google-sf.html       (individual job)
├── marketing-manager-facebook-ny.html      (individual job)
├── data-scientist-microsoft-seattle.html   (individual job)
└── ...

/companies/
├── index.html                              (companies archive)
├── amazon.html                             (company profile)
├── google.html                             (company profile)
├── microsoft.html                          (company profile)
└── ...

/categories/
├── index.html                              (categories overview)
├── software-engineering.html               (category page)
├── management.html                         (category page)
├── marketing.html                          (category page)
├── data-science.html                       (category page)
└── ...
```

## Asset Organization

### Assets Directory Structure
```
/assets/
├── css/
│   ├── site.css                    (main compiled CSS)
│   ├── critical.css                (above-fold styles)
│   ├── components/
│   │   ├── header.css              (component-specific)
│   │   ├── footer.css              (component-specific)
│   │   ├── sidebar.css             (component-specific)
│   │   └── forms.css               (component-specific)
│   └── pages/
│       ├── home.css                (page-specific)
│       ├── job-single.css          (page-specific)
│       └── archive.css             (page-specific)
├── js/
│   ├── site.js                     (main JavaScript bundle)
│   ├── vendor.js                   (third-party libraries)
│   ├── components/
│   │   ├── search.js               (search functionality)
│   │   ├── auth.js                 (authentication)
│   │   ├── forms.js                (form handling)
│   │   └── notifications.js        (notifications)
│   └── pages/
│       ├── job-application.js      (job-specific features)
│       └── company-profile.js      (company-specific features)
├── images/
│   ├── logos/                      (company logos)
│   ├── avatars/                    (user avatars)
│   ├── backgrounds/                (background images)
│   ├── icons/                      (UI icons)
│   └── optimized/                  (processed images)
│       ├── webp/                   (WebP format)
│       ├── avif/                   (AVIF format)
│       └── responsive/             (multiple sizes)
└── fonts/
    ├── primary/                    (main font family)
    ├── secondary/                  (accent font)
    └── icons/                      (icon fonts)
```

## Component File Organization

### Reusable Components
```
/components/
├── header.html                     (site header)
├── footer.html                     (site footer)
├── sidebar.html                    (sidebar content)
├── navigation.html                 (main navigation)
├── meta-tags.html                  (SEO meta tags)
├── breadcrumbs.html                (navigation breadcrumbs)
├── pagination.html                 (page navigation)
├── search-form.html                (search interface)
├── job-card.html                   (job listing card)
├── company-card.html               (company listing card)
└── social-share.html               (social sharing buttons)
```

### Template Structure
```
/templates/
├── layouts/
│   ├── base.html                   (master layout)
│   ├── single-column.html          (single column layout)
│   ├── two-column.html             (two column layout)
│   └── three-column.html           (three column layout)
├── pages/
│   ├── home.html                   (homepage template)
│   ├── job-single.html             (individual job page)
│   ├── job-archive.html            (job listing page)
│   ├── company-single.html         (company profile page)
│   ├── category-archive.html       (category listing page)
│   └── search-results.html         (search results page)
└── partials/
    ├── job-meta.html               (job metadata)
    ├── company-info.html           (company information)
    ├── related-jobs.html           (related job listings)
    └── contact-form.html           (contact form)
```

## URL Structure and Routing

### URL Patterns
```
Homepage:           /
Job Archive:        /jobs/
Job Single:         /jobs/{slug}.html
Company Archive:    /companies/
Company Single:     /companies/{slug}.html
Category Archive:   /categories/
Category Single:    /categories/{slug}.html
Location Archive:   /locations/
Location Single:    /locations/{slug}.html
Search Results:     /search/?q={query}
```

### Slug Generation Patterns
```javascript
const slugPatterns = {
  jobs: '{title}-{company}-{location}',
  companies: '{name}',
  categories: '{name}',
  locations: '{city}-{state}',
  
  // Examples:
  // site-manager-amazon-new-york.html
  // google.html
  // software-engineering.html
  // new-york-ny.html
}
```

### Pagination Structure
```
/jobs/                              (page 1)
/jobs/page-2.html                   (page 2)
/jobs/page-3.html                   (page 3)

/categories/software-engineering/   (page 1)
/categories/software-engineering/page-2.html
/categories/software-engineering/page-3.html
```

## File Naming Conventions

### HTML Files
```
Naming Pattern: {content-type}-{identifier}.html
Examples:
- job-site-manager-amazon-ny.html
- company-google.html
- category-software-engineering.html
- location-new-york-ny.html
```

### Asset Files
```
CSS Files:
- site.css (main stylesheet)
- critical.css (critical path CSS)
- component-{name}.css (component styles)
- page-{template}.css (page-specific styles)

JavaScript Files:
- site.js (main bundle)
- vendor.js (third-party libraries)
- component-{name}.js (component scripts)
- page-{template}.js (page-specific scripts)

Image Files:
- {type}-{identifier}-{size}.{format}
- logo-amazon-200x100.webp
- avatar-user123-150x150.jpg
- bg-hero-1920x1080.webp
```

## Data File Organization

### Content Data Structure
```
/data/
├── jobs/
│   ├── job-123.json                (individual job data)
│   ├── job-124.json                (individual job data)
│   └── index.json                  (jobs index/manifest)
├── companies/
│   ├── company-456.json            (individual company data)
│   ├── company-457.json            (individual company data)
│   └── index.json                  (companies index/manifest)
├── categories/
│   ├── category-789.json           (individual category data)
│   └── index.json                  (categories index/manifest)
└── manifests/
    ├── site-manifest.json          (complete site structure)
    ├── asset-manifest.json         (asset mapping)
    └── build-manifest.json         (build information)
```

### Manifest File Examples
```json
// site-manifest.json
{
  "pages": {
    "total": 1250,
    "jobs": 1000,
    "companies": 150,
    "categories": 50,
    "locations": 50
  },
  "lastBuild": "2024-01-15T10:30:00Z",
  "version": "1.2.3"
}

// asset-manifest.json
{
  "css": {
    "site": "/assets/css/site.css?v=abc123",
    "critical": "/assets/css/critical.css?v=def456"
  },
  "js": {
    "site": "/assets/js/site.js?v=ghi789",
    "vendor": "/assets/js/vendor.js?v=jkl012"
  }
}
```

## Archive and Listing Pages

### Archive Page Structure
```html
<!-- jobs/index.html -->
<!DOCTYPE html>
<html>
<head>
    <title>Jobs Archive | JobHub</title>
    <!-- Meta tags, CSS -->
</head>
<body>
    <!-- Header component -->
    
    <main>
        <h1>All Jobs</h1>
        
        <!-- Filter/Search interface -->
        <div class="filters">
            <!-- Static filter options -->
        </div>
        
        <!-- Job listings -->
        <div class="job-listings">
            <!-- Static job cards -->
        </div>
        
        <!-- Pagination -->
        <nav class="pagination">
            <!-- Static pagination links -->
        </nav>
    </main>
    
    <!-- Sidebar with dynamic components -->
    <!-- Footer component -->
</body>
</html>
```

### Category Page Structure
```html
<!-- categories/software-engineering.html -->
<!DOCTYPE html>
<html>
<head>
    <title>Software Engineering Jobs | JobHub</title>
    <!-- Category-specific meta tags -->
</head>
<body>
    <!-- Header -->
    
    <main>
        <h1>Software Engineering Jobs</h1>
        <p>Category description...</p>
        
        <!-- Jobs in this category -->
        <div class="category-jobs">
            <!-- Static job listings -->
        </div>
        
        <!-- Related categories -->
        <div class="related-categories">
            <!-- Static category links -->
        </div>
    </main>
    
    <!-- Sidebar and Footer -->
</body>
</html>
```

## SEO and Meta File Organization

### SEO Files
```
/
├── sitemap.xml                     (main sitemap)
├── sitemap-jobs.xml               (jobs sitemap)
├── sitemap-companies.xml          (companies sitemap)
├── sitemap-images.xml             (images sitemap)
├── robots.txt                     (crawler directives)
├── humans.txt                     (human-readable info)
└── .well-known/
    ├── security.txt               (security contact)
    └── ads.txt                    (advertising verification)
```

### Meta Tag Templates
```html
<!-- Standard meta tags -->
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>{{page_title}} | {{site_name}}</title>
<meta name="description" content="{{page_description}}">

<!-- Open Graph tags -->
<meta property="og:title" content="{{page_title}}">
<meta property="og:description" content="{{page_description}}">
<meta property="og:image" content="{{page_image}}">
<meta property="og:url" content="{{page_url}}">

<!-- Twitter Card tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{page_title}}">
<meta name="twitter:description" content="{{page_description}}">
<meta name="twitter:image" content="{{page_image}}">

<!-- Schema.org markup -->
<script type="application/ld+json">
{
  "@context": "https://schema.org/",
  "@type": "{{schema_type}}",
  "name": "{{item_name}}",
  "description": "{{item_description}}"
}
</script>
```
