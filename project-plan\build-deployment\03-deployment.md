# Deployment Strategies and CDN Integration

## Deployment Architecture

### Multi-Environment Strategy
```
Development → Staging → Production
     ↓           ↓          ↓
Local Build → Preview → Live Site
     ↓           ↓          ↓
File System → S3/Netlify → CDN
```

### Environment Configuration
```javascript
// deployment.config.js
export const deploymentConfig = {
  development: {
    target: 'local',
    buildCommand: 'npm run build:dev',
    outputDir: 'dist',
    serve: true,
    port: 3000,
    hotReload: true
  },
  
  staging: {
    target: 'netlify',
    buildCommand: 'npm run build:staging',
    outputDir: 'dist',
    domain: 'staging.jobhub.com',
    basicAuth: true,
    previewDeploys: true,
    branchDeploys: ['develop', 'feature/*']
  },
  
  production: {
    target: 'aws-s3',
    buildCommand: 'npm run build:prod',
    outputDir: 'dist',
    domain: 'jobhub.com',
    cdn: 'cloudfront',
    caching: 'aggressive',
    compression: true,
    monitoring: true
  }
};
```

## Static Site Hosting

### Netlify Deployment
```yaml
# netlify.toml
[build]
  command = "npm run build:prod"
  publish = "dist"
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://www.google-analytics.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

[[redirects]]
  from = "/jobs"
  to = "/jobs/"
  status = 301

[[redirects]]
  from = "/companies"
  to = "/companies/"
  status = 301

# SPA fallback for client-side routing
[[redirects]]
  from = "/dashboard/*"
  to = "/dashboard/index.html"
  status = 200

# WordPress backend proxy
[[redirects]]
  from = "/manager/*"
  to = "https://backend.jobhub.com/:splat"
  status = 200
  force = true
```

### AWS S3 + CloudFront Deployment
```javascript
// scripts/deploy-aws.js
import AWS from 'aws-sdk';
import fs from 'fs/promises';
import path from 'path';
import mime from 'mime-types';
import { createHash } from 'crypto';

class AWSDeployer {
  constructor(config) {
    this.config = config;
    this.s3 = new AWS.S3({
      accessKeyId: config.aws.accessKeyId,
      secretAccessKey: config.aws.secretAccessKey,
      region: config.aws.region
    });
    this.cloudfront = new AWS.CloudFront({
      accessKeyId: config.aws.accessKeyId,
      secretAccessKey: config.aws.secretAccessKey
    });
  }

  async deploy() {
    console.log('🚀 Starting AWS deployment...');
    
    const startTime = Date.now();
    
    // Upload files to S3
    const uploadedFiles = await this.uploadToS3();
    
    // Invalidate CloudFront cache
    await this.invalidateCloudFront(uploadedFiles);
    
    const duration = Date.now() - startTime;
    console.log(`✅ Deployment completed in ${(duration / 1000).toFixed(2)}s`);
  }

  async uploadToS3() {
    const files = await this.getFilesToUpload();
    const uploadedFiles = [];
    
    console.log(`📤 Uploading ${files.length} files to S3...`);
    
    for (const file of files) {
      const uploaded = await this.uploadFile(file);
      if (uploaded) {
        uploadedFiles.push(file.key);
      }
    }
    
    console.log(`✅ Uploaded ${uploadedFiles.length} files`);
    return uploadedFiles;
  }

  async getFilesToUpload() {
    const files = [];
    const distDir = this.config.build.outputDir;
    
    const walkDir = async (dir, prefix = '') => {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        const key = path.join(prefix, entry.name).replace(/\\/g, '/');
        
        if (entry.isDirectory()) {
          await walkDir(fullPath, key);
        } else {
          const content = await fs.readFile(fullPath);
          const hash = createHash('md5').update(content).digest('hex');
          
          files.push({
            localPath: fullPath,
            key: key,
            content: content,
            hash: hash,
            contentType: mime.lookup(fullPath) || 'application/octet-stream'
          });
        }
      }
    };
    
    await walkDir(distDir);
    return files;
  }

  async uploadFile(file) {
    try {
      // Check if file already exists with same hash
      const existing = await this.getS3ObjectMetadata(file.key);
      if (existing && existing.ETag === `"${file.hash}"`) {
        console.log(`⏭️  Skipping ${file.key} (unchanged)`);
        return false;
      }
      
      const params = {
        Bucket: this.config.aws.s3Bucket,
        Key: file.key,
        Body: file.content,
        ContentType: file.contentType,
        CacheControl: this.getCacheControl(file.key),
        Metadata: {
          'build-hash': file.hash,
          'build-timestamp': Date.now().toString()
        }
      };
      
      // Add compression for text files
      if (this.isCompressible(file.contentType)) {
        params.ContentEncoding = 'gzip';
        params.Body = await this.gzipContent(file.content);
      }
      
      await this.s3.upload(params).promise();
      console.log(`✅ Uploaded ${file.key}`);
      return true;
      
    } catch (error) {
      console.error(`❌ Failed to upload ${file.key}:`, error.message);
      return false;
    }
  }

  async getS3ObjectMetadata(key) {
    try {
      const result = await this.s3.headObject({
        Bucket: this.config.aws.s3Bucket,
        Key: key
      }).promise();
      return result;
    } catch (error) {
      if (error.code === 'NotFound') {
        return null;
      }
      throw error;
    }
  }

  getCacheControl(key) {
    // Static assets with hash in filename
    if (key.includes('/assets/') && /\.[a-f0-9]{8,}\./i.test(key)) {
      return 'public, max-age=********, immutable'; // 1 year
    }
    
    // HTML files
    if (key.endsWith('.html')) {
      return 'public, max-age=0, must-revalidate';
    }
    
    // Other assets
    if (key.includes('/assets/')) {
      return 'public, max-age=86400'; // 1 day
    }
    
    // Default
    return 'public, max-age=3600'; // 1 hour
  }

  isCompressible(contentType) {
    const compressibleTypes = [
      'text/',
      'application/javascript',
      'application/json',
      'application/xml',
      'image/svg+xml'
    ];
    
    return compressibleTypes.some(type => contentType.startsWith(type));
  }

  async gzipContent(content) {
    const zlib = await import('zlib');
    return new Promise((resolve, reject) => {
      zlib.gzip(content, (error, result) => {
        if (error) reject(error);
        else resolve(result);
      });
    });
  }

  async invalidateCloudFront(changedFiles) {
    if (!this.config.aws.cloudFrontDistributionId) {
      console.log('⏭️  Skipping CloudFront invalidation (no distribution ID)');
      return;
    }
    
    console.log('🔄 Invalidating CloudFront cache...');
    
    // Determine paths to invalidate
    const pathsToInvalidate = this.getInvalidationPaths(changedFiles);
    
    if (pathsToInvalidate.length === 0) {
      console.log('⏭️  No paths to invalidate');
      return;
    }
    
    const params = {
      DistributionId: this.config.aws.cloudFrontDistributionId,
      InvalidationBatch: {
        CallerReference: `deploy-${Date.now()}`,
        Paths: {
          Quantity: pathsToInvalidate.length,
          Items: pathsToInvalidate
        }
      }
    };
    
    try {
      const result = await this.cloudfront.createInvalidation(params).promise();
      console.log(`✅ CloudFront invalidation created: ${result.Invalidation.Id}`);
    } catch (error) {
      console.error('❌ CloudFront invalidation failed:', error.message);
    }
  }

  getInvalidationPaths(changedFiles) {
    const paths = new Set();
    
    for (const file of changedFiles) {
      // Add the file itself
      paths.add(`/${file}`);
      
      // If it's an HTML file, also invalidate without .html extension
      if (file.endsWith('.html') && file !== 'index.html') {
        const pathWithoutExtension = `/${file.replace('.html', '')}`;
        paths.add(pathWithoutExtension);
      }
      
      // If index.html changed, invalidate root
      if (file.endsWith('/index.html')) {
        const dir = file.replace('/index.html', '');
        paths.add(dir === '' ? '/' : `/${dir}/`);
      }
    }
    
    return Array.from(paths);
  }
}

export default AWSDeployer;
```

## Continuous Deployment

### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Build and Deploy

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  CACHE_KEY: node-modules-${{ hashFiles('**/package-lock.json') }}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm run test
      
      - name: Run linting
        run: npm run lint
      
      - name: Check types
        run: npm run type-check

  build:
    needs: test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment: [staging, production]
        include:
          - environment: staging
            branch: develop
          - environment: production
            branch: main
    
    if: github.ref == format('refs/heads/{0}', matrix.branch)
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build for ${{ matrix.environment }}
        run: npm run build:${{ matrix.environment }}
        env:
          WORDPRESS_API_URL: ${{ secrets[format('WORDPRESS_API_URL_{0}', upper(matrix.environment))] }}
          SITE_URL: ${{ secrets[format('SITE_URL_{0}', upper(matrix.environment))] }}
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-${{ matrix.environment }}
          path: dist/
          retention-days: 7

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-staging
          path: dist/
      
      - name: Deploy to Netlify
        uses: netlify/actions/cli@master
        with:
          args: deploy --prod --dir=dist
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID_STAGING }}

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-production
          path: dist/
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Deploy to S3
        run: |
          aws s3 sync dist/ s3://${{ secrets.S3_BUCKET }} --delete --cache-control "public, max-age=********" --exclude "*.html"
          aws s3 sync dist/ s3://${{ secrets.S3_BUCKET }} --delete --cache-control "public, max-age=0, must-revalidate" --include "*.html"
      
      - name: Invalidate CloudFront
        run: |
          aws cloudfront create-invalidation --distribution-id ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID }} --paths "/*"

  lighthouse:
    needs: deploy-production
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v9
        with:
          urls: |
            https://jobhub.com
            https://jobhub.com/jobs/
            https://jobhub.com/companies/
          configPath: './lighthouserc.json'
          uploadArtifacts: true
          temporaryPublicStorage: true
```

## Monitoring and Health Checks

### Deployment Health Checks
```javascript
// scripts/health-check.js
import axios from 'axios';

class HealthChecker {
  constructor(config) {
    this.config = config;
    this.checks = [
      this.checkHomepage,
      this.checkJobPages,
      this.checkAssets,
      this.checkPerformance,
      this.checkSEO
    ];
  }

  async runAllChecks() {
    console.log('🔍 Running deployment health checks...');
    
    const results = [];
    
    for (const check of this.checks) {
      try {
        const result = await check.call(this);
        results.push(result);
        console.log(`✅ ${result.name}: ${result.status}`);
      } catch (error) {
        results.push({
          name: check.name,
          status: 'failed',
          error: error.message
        });
        console.error(`❌ ${check.name}: ${error.message}`);
      }
    }
    
    const passed = results.filter(r => r.status === 'passed').length;
    const total = results.length;
    
    console.log(`\n📊 Health Check Summary: ${passed}/${total} checks passed`);
    
    if (passed < total) {
      throw new Error(`Health checks failed: ${total - passed} checks failed`);
    }
    
    return results;
  }

  async checkHomepage() {
    const response = await axios.get(this.config.siteUrl, {
      timeout: 10000,
      headers: { 'User-Agent': 'HealthCheck/1.0' }
    });
    
    if (response.status !== 200) {
      throw new Error(`Homepage returned status ${response.status}`);
    }
    
    const html = response.data;
    
    // Check for essential elements
    const checks = [
      { name: 'title', pattern: /<title>.*<\/title>/ },
      { name: 'meta description', pattern: /<meta name="description"/ },
      { name: 'main content', pattern: /<main/ },
      { name: 'navigation', pattern: /<nav/ }
    ];
    
    for (const check of checks) {
      if (!check.pattern.test(html)) {
        throw new Error(`Missing ${check.name}`);
      }
    }
    
    return { name: 'Homepage', status: 'passed' };
  }

  async checkJobPages() {
    const jobUrls = [
      `${this.config.siteUrl}/jobs/`,
      `${this.config.siteUrl}/jobs/sample-job-company-location.html`
    ];
    
    for (const url of jobUrls) {
      const response = await axios.get(url, { timeout: 10000 });
      
      if (response.status !== 200) {
        throw new Error(`Job page ${url} returned status ${response.status}`);
      }
    }
    
    return { name: 'Job Pages', status: 'passed' };
  }

  async checkAssets() {
    const assetUrls = [
      `${this.config.siteUrl}/assets/css/site.css`,
      `${this.config.siteUrl}/assets/js/site.js`
    ];
    
    for (const url of assetUrls) {
      const response = await axios.head(url, { timeout: 5000 });
      
      if (response.status !== 200) {
        throw new Error(`Asset ${url} returned status ${response.status}`);
      }
      
      // Check cache headers
      const cacheControl = response.headers['cache-control'];
      if (!cacheControl || !cacheControl.includes('max-age')) {
        throw new Error(`Asset ${url} missing cache headers`);
      }
    }
    
    return { name: 'Assets', status: 'passed' };
  }

  async checkPerformance() {
    // Use Lighthouse CI or similar tool
    const lighthouseUrl = `https://www.googleapis.com/pagespeedonline/v5/runPagespeed?url=${encodeURIComponent(this.config.siteUrl)}&category=performance`;
    
    const response = await axios.get(lighthouseUrl, { timeout: 30000 });
    const score = response.data.lighthouseResult.categories.performance.score;
    
    if (score < 0.9) {
      throw new Error(`Performance score ${score} below threshold 0.9`);
    }
    
    return { 
      name: 'Performance', 
      status: 'passed', 
      score: score 
    };
  }

  async checkSEO() {
    const response = await axios.get(this.config.siteUrl, { timeout: 10000 });
    const html = response.data;
    
    // Check essential SEO elements
    const seoChecks = [
      { name: 'canonical URL', pattern: /<link rel="canonical"/ },
      { name: 'Open Graph', pattern: /<meta property="og:/ },
      { name: 'structured data', pattern: /<script type="application\/ld\+json">/ },
      { name: 'meta viewport', pattern: /<meta name="viewport"/ }
    ];
    
    for (const check of seoChecks) {
      if (!check.pattern.test(html)) {
        throw new Error(`Missing ${check.name}`);
      }
    }
    
    return { name: 'SEO', status: 'passed' };
  }
}

export default HealthChecker;
```

### Rollback Strategy
```javascript
// scripts/rollback.js
class RollbackManager {
  constructor(config) {
    this.config = config;
    this.deploymentHistory = [];
  }

  async rollback(targetVersion) {
    console.log(`🔄 Rolling back to version ${targetVersion}...`);
    
    // Get deployment artifact
    const artifact = await this.getDeploymentArtifact(targetVersion);
    
    if (!artifact) {
      throw new Error(`Deployment artifact for version ${targetVersion} not found`);
    }
    
    // Deploy previous version
    await this.deployArtifact(artifact);
    
    // Verify rollback
    await this.verifyRollback(targetVersion);
    
    console.log(`✅ Rollback to version ${targetVersion} completed`);
  }

  async getDeploymentArtifact(version) {
    // Implementation depends on storage system
    // Could be S3, GitHub releases, etc.
    return null;
  }

  async deployArtifact(artifact) {
    // Deploy the artifact using the same process as normal deployment
  }

  async verifyRollback(version) {
    // Run health checks to verify rollback was successful
    const healthChecker = new HealthChecker(this.config);
    await healthChecker.runAllChecks();
  }
}
```
