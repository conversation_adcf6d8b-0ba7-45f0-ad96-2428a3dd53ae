# Static Components Architecture

## Static Component Philosophy

### Pure HTML Components
Static components are standalone HTML files that contain no dynamic content and require no JavaScript to function. They provide the core structure and content that loads instantly.

### Component Characteristics
- **Self-contained**: Complete HTML with embedded content
- **No API Dependencies**: All data is pre-rendered during build
- **SEO Optimized**: Fully crawlable by search engines
- **Fast Loading**: No JavaScript required for initial render
- **Accessible**: Works with screen readers and assistive technologies

## Core Static Components

### Header Component
```html
<!-- components/header.html -->
<header class="header" role="banner">
  <div class="header__container">
    <a href="/" class="header__logo" aria-label="JobHub Homepage">
      <img src="/assets/images/logo.svg" alt="JobHub" class="header__logo-image">
      <span class="header__logo-text">JobHub</span>
    </a>
    
    <nav class="header__nav" role="navigation" aria-label="Main navigation">
      <ul class="nav__list">
        <li class="nav__item">
          <a href="/" class="nav__link nav__link--active">Home</a>
        </li>
        <li class="nav__item">
          <a href="/jobs/" class="nav__link">Browse Jobs</a>
        </li>
        <li class="nav__item">
          <a href="/companies/" class="nav__link">Companies</a>
        </li>
        <li class="nav__item">
          <a href="/categories/" class="nav__link">Categories</a>
        </li>
        <li class="nav__item">
          <a href="/about/" class="nav__link">About</a>
        </li>
      </ul>
    </nav>
    
    <div class="header__actions">
      <!-- Placeholder for dynamic auth widget -->
      <div id="auth-widget-mount"></div>
      
      <!-- Static search form -->
      <form class="header__search" action="/search/" method="get">
        <input 
          type="search" 
          name="q" 
          placeholder="Search jobs..." 
          class="header__search-input"
          aria-label="Search jobs">
        <button type="submit" class="header__search-button" aria-label="Submit search">
          <svg class="header__search-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
          </svg>
        </button>
      </form>
      
      <button class="header__menu-toggle" aria-label="Toggle mobile menu">
        <span class="header__menu-icon"></span>
      </button>
    </div>
  </div>
</header>
```

### Footer Component
```html
<!-- components/footer.html -->
<footer class="footer" role="contentinfo">
  <div class="footer__container">
    <div class="footer__sections">
      <div class="footer__section">
        <h3 class="footer__section-title">Quick Links</h3>
        <ul class="footer__links">
          <li><a href="/jobs/" class="footer__link">Browse Jobs</a></li>
          <li><a href="/companies/" class="footer__link">Companies</a></li>
          <li><a href="/categories/" class="footer__link">Categories</a></li>
          <li><a href="/locations/" class="footer__link">Locations</a></li>
        </ul>
      </div>
      
      <div class="footer__section">
        <h3 class="footer__section-title">For Employers</h3>
        <ul class="footer__links">
          <li><a href="/post-job/" class="footer__link">Post a Job</a></li>
          <li><a href="/employer-dashboard/" class="footer__link">Employer Dashboard</a></li>
          <li><a href="/pricing/" class="footer__link">Pricing</a></li>
          <li><a href="/employer-resources/" class="footer__link">Resources</a></li>
        </ul>
      </div>
      
      <div class="footer__section">
        <h3 class="footer__section-title">Support</h3>
        <ul class="footer__links">
          <li><a href="/help/" class="footer__link">Help Center</a></li>
          <li><a href="/contact/" class="footer__link">Contact Us</a></li>
          <li><a href="/privacy/" class="footer__link">Privacy Policy</a></li>
          <li><a href="/terms/" class="footer__link">Terms of Service</a></li>
        </ul>
      </div>
      
      <div class="footer__section">
        <h3 class="footer__section-title">Connect</h3>
        <div class="footer__social">
          <a href="https://twitter.com/jobhub" class="footer__social-link" aria-label="Follow us on Twitter">
            <svg class="footer__social-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M23 3a10.9 10.9 0 01-3.14 1.53 4.48 4.48 0 00-7.86 3v1A10.66 10.66 0 013 4s-4 9 5 13a11.64 11.64 0 01-7 2c9 5 20 0 20-11.5a4.5 4.5 0 00-.08-.83A7.72 7.72 0 0023 3z"/>
            </svg>
          </a>
          <a href="https://linkedin.com/company/jobhub" class="footer__social-link" aria-label="Follow us on LinkedIn">
            <svg class="footer__social-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M16 8a6 6 0 016 6v7h-4v-7a2 2 0 00-2-2 2 2 0 00-2 2v7h-4v-7a6 6 0 016-6zM2 9h4v12H2z"/>
              <circle cx="4" cy="4" r="2"/>
            </svg>
          </a>
        </div>
        
        <div class="footer__newsletter">
          <h4 class="footer__newsletter-title">Stay Updated</h4>
          <!-- Placeholder for dynamic newsletter signup -->
          <div id="newsletter-widget-mount"></div>
        </div>
      </div>
    </div>
    
    <div class="footer__bottom">
      <div class="footer__copyright">
        <p>&copy; 2024 JobHub. All rights reserved.</p>
      </div>
      <div class="footer__legal">
        <a href="/privacy/" class="footer__legal-link">Privacy</a>
        <a href="/terms/" class="footer__legal-link">Terms</a>
        <a href="/cookies/" class="footer__legal-link">Cookies</a>
      </div>
    </div>
  </div>
</footer>
```

### Sidebar Component
```html
<!-- components/sidebar.html -->
<aside class="sidebar" role="complementary" aria-label="Sidebar content">
  <div class="sidebar__section">
    <h3 class="sidebar__title">Latest Jobs</h3>
    <div class="sidebar__content">
      <div class="job-mini-card">
        <h4 class="job-mini-card__title">
          <a href="/jobs/senior-developer-tech-corp-sf.html" class="job-mini-card__link">
            Senior Developer at Tech Corp
          </a>
        </h4>
        <p class="job-mini-card__location">San Francisco, CA</p>
        <p class="job-mini-card__salary">$120k - $150k</p>
      </div>
      
      <div class="job-mini-card">
        <h4 class="job-mini-card__title">
          <a href="/jobs/product-manager-startup-ny.html" class="job-mini-card__link">
            Product Manager at Startup
          </a>
        </h4>
        <p class="job-mini-card__location">New York, NY</p>
        <p class="job-mini-card__salary">$100k - $130k</p>
      </div>
      
      <div class="job-mini-card">
        <h4 class="job-mini-card__title">
          <a href="/jobs/ux-designer-agency-la.html" class="job-mini-card__link">
            UX Designer at Agency
          </a>
        </h4>
        <p class="job-mini-card__location">Los Angeles, CA</p>
        <p class="job-mini-card__salary">$80k - $110k</p>
      </div>
    </div>
    
    <a href="/jobs/" class="sidebar__view-all">View All Jobs →</a>
  </div>
  
  <div class="sidebar__section">
    <h3 class="sidebar__title">Browse by Category</h3>
    <div class="sidebar__content">
      <ul class="category-list">
        <li class="category-list__item">
          <a href="/categories/software-engineering.html" class="category-list__link">
            Software Engineering
            <span class="category-list__count">(245)</span>
          </a>
        </li>
        <li class="category-list__item">
          <a href="/categories/product-management.html" class="category-list__link">
            Product Management
            <span class="category-list__count">(89)</span>
          </a>
        </li>
        <li class="category-list__item">
          <a href="/categories/design.html" class="category-list__link">
            Design
            <span class="category-list__count">(156)</span>
          </a>
        </li>
        <li class="category-list__item">
          <a href="/categories/marketing.html" class="category-list__link">
            Marketing
            <span class="category-list__count">(123)</span>
          </a>
        </li>
        <li class="category-list__item">
          <a href="/categories/sales.html" class="category-list__link">
            Sales
            <span class="category-list__count">(98)</span>
          </a>
        </li>
      </ul>
    </div>
    
    <a href="/categories/" class="sidebar__view-all">View All Categories →</a>
  </div>
  
  <div class="sidebar__section">
    <h3 class="sidebar__title">Featured Companies</h3>
    <div class="sidebar__content">
      <div class="company-mini-card">
        <img src="/assets/images/logos/google.png" alt="Google" class="company-mini-card__logo">
        <div class="company-mini-card__info">
          <h4 class="company-mini-card__name">
            <a href="/companies/google.html" class="company-mini-card__link">Google</a>
          </h4>
          <p class="company-mini-card__jobs">15 open positions</p>
        </div>
      </div>
      
      <div class="company-mini-card">
        <img src="/assets/images/logos/microsoft.png" alt="Microsoft" class="company-mini-card__logo">
        <div class="company-mini-card__info">
          <h4 class="company-mini-card__name">
            <a href="/companies/microsoft.html" class="company-mini-card__link">Microsoft</a>
          </h4>
          <p class="company-mini-card__jobs">23 open positions</p>
        </div>
      </div>
      
      <div class="company-mini-card">
        <img src="/assets/images/logos/amazon.png" alt="Amazon" class="company-mini-card__logo">
        <div class="company-mini-card__info">
          <h4 class="company-mini-card__name">
            <a href="/companies/amazon.html" class="company-mini-card__link">Amazon</a>
          </h4>
          <p class="company-mini-card__jobs">31 open positions</p>
        </div>
      </div>
    </div>
    
    <a href="/companies/" class="sidebar__view-all">View All Companies →</a>
  </div>
  
  <!-- Placeholder for dynamic widgets -->
  <div class="sidebar__section">
    <div id="job-alerts-widget-mount"></div>
  </div>
</aside>
```

## Content Template Components

### Job Card Component
```html
<!-- components/job-card.html -->
<article class="job-card" itemscope itemtype="https://schema.org/JobPosting">
  <div class="job-card__header">
    <div class="job-card__main">
      <h3 class="job-card__title" itemprop="title">
        <a href="{{job_url}}" class="job-card__title-link">{{job_title}}</a>
      </h3>
      <div class="job-card__company" itemprop="hiringOrganization" itemscope itemtype="https://schema.org/Organization">
        <a href="{{company_url}}" class="job-card__company-link" itemprop="name">{{company_name}}</a>
      </div>
    </div>
    <div class="job-card__featured-badge" aria-hidden="true">
      {{#if featured}}
      <span class="badge badge--featured">Featured</span>
      {{/if}}
    </div>
  </div>
  
  <div class="job-card__meta">
    <div class="job-card__meta-item" itemprop="jobLocation" itemscope itemtype="https://schema.org/Place">
      <svg class="job-card__meta-icon" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
      </svg>
      <span itemprop="name">{{location}}</span>
    </div>
    
    <div class="job-card__meta-item">
      <svg class="job-card__meta-icon" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
      </svg>
      <span>{{job_type}}</span>
    </div>
    
    {{#if salary}}
    <div class="job-card__meta-item" itemprop="baseSalary" itemscope itemtype="https://schema.org/MonetaryAmount">
      <svg class="job-card__meta-icon" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z"/>
      </svg>
      <span itemprop="value">{{salary}}</span>
    </div>
    {{/if}}
    
    <div class="job-card__meta-item">
      <svg class="job-card__meta-icon" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2-7v2H3V4h3.5l1-1h5l1 1H17zm-2 4H5v12c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V8z"/>
      </svg>
      <time datetime="{{posted_date_iso}}" itemprop="datePosted">{{posted_date_human}}</time>
    </div>
  </div>
  
  <div class="job-card__description" itemprop="description">
    {{job_excerpt}}
  </div>
  
  <div class="job-card__footer">
    <div class="job-card__tags">
      {{#each skills}}
      <span class="tag tag--skill">{{this}}</span>
      {{/each}}
    </div>
    
    <div class="job-card__actions">
      <!-- Placeholder for dynamic save button -->
      <div class="job-card__save-mount" data-job-id="{{job_id}}"></div>
      
      <a href="{{job_url}}" class="btn btn--primary btn--small">
        View Details
      </a>
    </div>
  </div>
</article>
```

### Breadcrumbs Component
```html
<!-- components/breadcrumbs.html -->
<nav class="breadcrumbs" aria-label="Breadcrumb navigation" role="navigation">
  <ol class="breadcrumbs__list" itemscope itemtype="https://schema.org/BreadcrumbList">
    <li class="breadcrumbs__item" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
      <a href="/" class="breadcrumbs__link" itemprop="item">
        <span itemprop="name">Home</span>
      </a>
      <meta itemprop="position" content="1">
    </li>
    
    {{#each breadcrumbs}}
    <li class="breadcrumbs__item" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
      {{#if url}}
      <a href="{{url}}" class="breadcrumbs__link" itemprop="item">
        <span itemprop="name">{{name}}</span>
      </a>
      {{else}}
      <span class="breadcrumbs__current" itemprop="name" aria-current="page">{{name}}</span>
      {{/if}}
      <meta itemprop="position" content="{{@index}}">
    </li>
    {{/each}}
  </ol>
</nav>
```

### Pagination Component
```html
<!-- components/pagination.html -->
<nav class="pagination" aria-label="Page navigation" role="navigation">
  <div class="pagination__info">
    <p class="pagination__text">
      Showing {{start_item}} to {{end_item}} of {{total_items}} results
    </p>
  </div>
  
  <div class="pagination__controls">
    {{#if has_previous}}
    <a href="{{previous_url}}" class="pagination__link pagination__link--prev" rel="prev">
      <svg class="pagination__icon" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
      </svg>
      Previous
    </a>
    {{else}}
    <span class="pagination__link pagination__link--disabled">
      <svg class="pagination__icon" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
      </svg>
      Previous
    </span>
    {{/if}}
    
    <div class="pagination__pages">
      {{#each pages}}
      {{#if current}}
      <span class="pagination__page pagination__page--current" aria-current="page">{{number}}</span>
      {{else if url}}
      <a href="{{url}}" class="pagination__page">{{number}}</a>
      {{else}}
      <span class="pagination__page pagination__page--ellipsis">…</span>
      {{/if}}
      {{/each}}
    </div>
    
    {{#if has_next}}
    <a href="{{next_url}}" class="pagination__link pagination__link--next" rel="next">
      Next
      <svg class="pagination__icon" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
      </svg>
    </a>
    {{else}}
    <span class="pagination__link pagination__link--disabled">
      Next
      <svg class="pagination__icon" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
      </svg>
    </span>
    {{/if}}
  </div>
</nav>
```

## SEO and Meta Components

### Meta Tags Component
```html
<!-- components/meta-tags.html -->
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="robots" content="{{robots_content}}">

<title>{{page_title}}</title>
<meta name="description" content="{{page_description}}">
<meta name="keywords" content="{{page_keywords}}">

<link rel="canonical" href="{{canonical_url}}">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="{{og_type}}">
<meta property="og:url" content="{{page_url}}">
<meta property="og:title" content="{{og_title}}">
<meta property="og:description" content="{{og_description}}">
<meta property="og:image" content="{{og_image}}">
<meta property="og:site_name" content="{{site_name}}">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="{{page_url}}">
<meta property="twitter:title" content="{{twitter_title}}">
<meta property="twitter:description" content="{{twitter_description}}">
<meta property="twitter:image" content="{{twitter_image}}">

<!-- Favicon -->
<link rel="icon" type="image/x-icon" href="/favicon.ico">
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">

<!-- Schema.org markup -->
<script type="application/ld+json">
{{{schema_markup}}}
</script>
```
