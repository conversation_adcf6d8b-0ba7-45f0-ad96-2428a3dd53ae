# Component Integration and Hydration Strategy

## Integration Architecture

### Hybrid Static-Dynamic Approach
The integration strategy combines static HTML components with selective React hydration for interactive features, ensuring optimal performance and user experience.

### Component Mounting Strategy
```javascript
// Mount points are defined in static HTML
<div id="auth-widget-mount"></div>
<div id="search-widget-mount"></div>
<div id="job-application-form-mount" data-job-id="123"></div>
<div class="job-card__save-mount" data-job-id="456"></div>
```

## Hydration System

### Selective Hydration Implementation
```javascript
// src/hydration/index.js
import React from 'react';
import ReactDOM from 'react-dom/client';
import { AuthWidget } from '../components/AuthWidget';
import { SearchWidget } from '../components/SearchWidget';
import { JobApplicationForm } from '../components/JobApplicationForm';
import { SaveJobWidget } from '../components/SaveJobWidget';
import { NotificationCenter } from '../components/NotificationCenter';

// Component registry
const COMPONENTS = {
  'auth-widget': AuthWidget,
  'search-widget': SearchWidget,
  'job-application-form': JobApplicationForm,
  'save-job-widget': SaveJobWidget,
  'notification-center': NotificationCenter
};

// Hydration manager
class HydrationManager {
  constructor() {
    this.mountedComponents = new Map();
    this.observers = new Map();
  }

  // Initialize hydration on page load
  init() {
    // Hydrate immediately visible components
    this.hydrateVisibleComponents();
    
    // Set up intersection observer for lazy hydration
    this.setupLazyHydration();
    
    // Set up mutation observer for dynamic content
    this.setupDynamicHydration();
  }

  // Hydrate components that are immediately visible
  hydrateVisibleComponents() {
    const criticalComponents = [
      'auth-widget-mount',
      'search-widget-mount',
      'notification-center-mount'
    ];

    criticalComponents.forEach(mountId => {
      const mountPoint = document.getElementById(mountId);
      if (mountPoint) {
        this.hydrateComponent(mountPoint);
      }
    });
  }

  // Set up lazy hydration for below-fold components
  setupLazyHydration() {
    const lazyMountPoints = document.querySelectorAll('[data-hydrate="lazy"]');
    
    if (lazyMountPoints.length === 0) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.hydrateComponent(entry.target);
            observer.unobserve(entry.target);
          }
        });
      },
      { rootMargin: '50px' }
    );

    lazyMountPoints.forEach(mountPoint => {
      observer.observe(mountPoint);
    });
  }

  // Set up dynamic hydration for content loaded via AJAX
  setupDynamicHydration() {
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added node contains mount points
            const mountPoints = node.querySelectorAll('[id$="-mount"], [class*="mount"]');
            mountPoints.forEach(mountPoint => {
              if (!this.mountedComponents.has(mountPoint)) {
                this.hydrateComponent(mountPoint);
              }
            });
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.observers.set('mutation', observer);
  }

  // Hydrate a specific component
  hydrateComponent(mountPoint) {
    const componentType = this.getComponentType(mountPoint);
    const Component = COMPONENTS[componentType];

    if (!Component) {
      console.warn(`Component type "${componentType}" not found`);
      return;
    }

    try {
      const props = this.extractProps(mountPoint);
      const root = ReactDOM.createRoot(mountPoint);
      
      root.render(React.createElement(Component, props));
      
      this.mountedComponents.set(mountPoint, {
        component: Component,
        root: root,
        props: props
      });

      // Mark as hydrated
      mountPoint.setAttribute('data-hydrated', 'true');
      
    } catch (error) {
      console.error(`Failed to hydrate component at`, mountPoint, error);
    }
  }

  // Determine component type from mount point
  getComponentType(mountPoint) {
    const id = mountPoint.id;
    const className = mountPoint.className;

    // Extract from ID
    if (id.endsWith('-mount')) {
      return id.replace('-mount', '').replace(/-/g, '-');
    }

    // Extract from class name
    if (className.includes('mount')) {
      const match = className.match(/(\w+)-mount/);
      return match ? match[1].replace(/-/g, '-') : null;
    }

    // Extract from data attribute
    return mountPoint.dataset.component;
  }

  // Extract props from data attributes
  extractProps(mountPoint) {
    const props = {};
    
    // Extract all data attributes as props
    Object.keys(mountPoint.dataset).forEach(key => {
      if (key !== 'component' && key !== 'hydrate') {
        const value = mountPoint.dataset[key];
        
        // Try to parse as JSON, fallback to string
        try {
          props[key] = JSON.parse(value);
        } catch {
          props[key] = value;
        }
      }
    });

    return props;
  }

  // Cleanup method for SPA navigation
  cleanup() {
    this.mountedComponents.forEach(({ root }) => {
      root.unmount();
    });
    this.mountedComponents.clear();

    this.observers.forEach(observer => {
      observer.disconnect();
    });
    this.observers.clear();
  }
}

// Initialize hydration manager
const hydrationManager = new HydrationManager();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    hydrationManager.init();
  });
} else {
  hydrationManager.init();
}

// Export for manual control
export default hydrationManager;
```

## Component Communication

### Event-Based Communication
```javascript
// src/utils/eventBus.js
class EventBus {
  constructor() {
    this.events = {};
  }

  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  off(event, callback) {
    if (this.events[event]) {
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    }
  }

  emit(event, data) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(data));
    }
  }
}

export const eventBus = new EventBus();

// Usage in components
import { eventBus } from '../utils/eventBus';

// In AuthWidget
const handleLogin = (user) => {
  eventBus.emit('user:login', user);
};

// In other components
useEffect(() => {
  const handleUserLogin = (user) => {
    // Update component state
  };
  
  eventBus.on('user:login', handleUserLogin);
  
  return () => {
    eventBus.off('user:login', handleUserLogin);
  };
}, []);
```

### Global State Management
```javascript
// src/store/globalStore.js
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export const useGlobalStore = create(
  persist(
    (set, get) => ({
      // User state
      user: null,
      isAuthenticated: false,
      
      // UI state
      theme: 'light',
      sidebarOpen: false,
      
      // Job-related state
      savedJobs: [],
      recentSearches: [],
      
      // Actions
      setUser: (user) => set({ user, isAuthenticated: !!user }),
      logout: () => set({ user: null, isAuthenticated: false, savedJobs: [] }),
      
      toggleTheme: () => set((state) => ({ 
        theme: state.theme === 'light' ? 'dark' : 'light' 
      })),
      
      addSavedJob: (jobId) => set((state) => ({
        savedJobs: [...new Set([...state.savedJobs, jobId])]
      })),
      
      removeSavedJob: (jobId) => set((state) => ({
        savedJobs: state.savedJobs.filter(id => id !== jobId)
      })),
      
      addRecentSearch: (query) => set((state) => ({
        recentSearches: [
          query,
          ...state.recentSearches.filter(q => q !== query)
        ].slice(0, 10)
      }))
    }),
    {
      name: 'jobhub-storage',
      partialize: (state) => ({
        theme: state.theme,
        savedJobs: state.savedJobs,
        recentSearches: state.recentSearches
      })
    }
  )
);
```

## Performance Optimization

### Code Splitting Strategy
```javascript
// src/components/lazy/index.js
import { lazy } from 'react';

// Lazy load heavy components
export const JobApplicationForm = lazy(() => 
  import('../JobApplicationForm').then(module => ({
    default: module.JobApplicationForm
  }))
);

export const CompanyDashboard = lazy(() => 
  import('../CompanyDashboard').then(module => ({
    default: module.CompanyDashboard
  }))
);

export const AdvancedSearch = lazy(() => 
  import('../AdvancedSearch').then(module => ({
    default: module.AdvancedSearch
  }))
);

// Lazy hydration wrapper
import React, { Suspense } from 'react';

export const LazyComponent = ({ component: Component, fallback, ...props }) => (
  <Suspense fallback={fallback || <div className="skeleton skeleton-text"></div>}>
    <Component {...props} />
  </Suspense>
);
```

### Bundle Optimization
```javascript
// webpack.config.js - Component splitting
module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        // Vendor libraries
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        
        // Common components
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true,
        },
        
        // Heavy components
        heavy: {
          test: /[\\/]src[\\/]components[\\/](JobApplicationForm|CompanyDashboard|AdvancedSearch)/,
          name: 'heavy-components',
          chunks: 'all',
        }
      }
    }
  }
};
```

## Error Handling and Fallbacks

### Component Error Boundaries
```javascript
// src/components/ErrorBoundary.jsx
import React from 'react';

class ComponentErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Component error:', error, errorInfo);
    
    // Report to error tracking service
    if (window.errorTracker) {
      window.errorTracker.captureException(error, {
        extra: errorInfo,
        tags: {
          component: this.props.componentName || 'unknown'
        }
      });
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="component-error">
          <p>Something went wrong with this component.</p>
          <button 
            onClick={() => this.setState({ hasError: false, error: null })}
            className="btn btn--small"
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ComponentErrorBoundary;
```

### Graceful Degradation
```javascript
// src/utils/featureDetection.js
export const featureSupport = {
  intersectionObserver: 'IntersectionObserver' in window,
  mutationObserver: 'MutationObserver' in window,
  webComponents: 'customElements' in window,
  serviceWorker: 'serviceWorker' in navigator,
  
  // Fallback strategies
  getFallbackStrategy(feature) {
    const fallbacks = {
      intersectionObserver: 'immediate-hydration',
      mutationObserver: 'manual-refresh',
      webComponents: 'react-components',
      serviceWorker: 'no-offline'
    };
    
    return fallbacks[feature] || 'basic-functionality';
  }
};

// Usage in hydration manager
if (!featureSupport.intersectionObserver) {
  // Fallback to immediate hydration
  this.hydrateAllComponents();
} else {
  this.setupLazyHydration();
}
```

## Testing Strategy

### Component Integration Tests
```javascript
// src/tests/integration/hydration.test.js
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import hydrationManager from '../hydration';

describe('Component Hydration', () => {
  beforeEach(() => {
    document.body.innerHTML = '';
    hydrationManager.cleanup();
  });

  test('should hydrate auth widget on page load', async () => {
    document.body.innerHTML = `
      <div id="auth-widget-mount" data-user-id="123"></div>
    `;

    hydrationManager.init();

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    });
  });

  test('should lazy hydrate components below fold', async () => {
    document.body.innerHTML = `
      <div style="height: 2000px;"></div>
      <div id="job-application-form-mount" data-hydrate="lazy" data-job-id="456"></div>
    `;

    hydrationManager.init();

    // Component should not be hydrated initially
    expect(screen.queryByRole('form')).not.toBeInTheDocument();

    // Scroll component into view
    const mountPoint = document.getElementById('job-application-form-mount');
    mountPoint.scrollIntoView();

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });
  });

  test('should handle hydration errors gracefully', async () => {
    // Mock console.error to avoid test noise
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    document.body.innerHTML = `
      <div id="invalid-component-mount"></div>
    `;

    hydrationManager.init();

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Component type "invalid-component" not found')
      );
    });

    consoleSpy.mockRestore();
  });
});
```

### Performance Monitoring
```javascript
// src/utils/performanceMonitor.js
export class PerformanceMonitor {
  static measureHydration(componentName, startTime) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    console.log(`${componentName} hydration took ${duration.toFixed(2)}ms`);
    
    // Report to analytics
    if (window.analytics) {
      window.analytics.track('Component Hydration', {
        component: componentName,
        duration: duration,
        timestamp: Date.now()
      });
    }
    
    // Performance budget check
    if (duration > 100) {
      console.warn(`${componentName} hydration exceeded 100ms budget`);
    }
  }
  
  static measureBundleSize(bundleName, size) {
    console.log(`${bundleName} bundle size: ${(size / 1024).toFixed(2)}KB`);
    
    if (window.analytics) {
      window.analytics.track('Bundle Size', {
        bundle: bundleName,
        size: size,
        sizeKB: size / 1024
      });
    }
  }
}
```
