# Advanced Skeleton Loading System

## Skeleton Loading Architecture

### Zero Layout Shift Strategy
```
Content Loading Flow:
├── Initial Page Load
│   ├── Critical CSS (with skeleton styles)
│   ├── Skeleton Placeholders (inline)
│   └── Content Hydration
├── Dynamic Content Loading
│   ├── Show Skeleton
│   ├── Fetch Data
│   ├── Render Content
│   └── Fade Out Skeleton
└── AdSense Loading
    ├── Ad Skeleton (specific dimensions)
    ├── AdSense Script Load
    ├── Ad Render
    └── Replace Skeleton
```

### Skeleton Component System
```javascript
// src/js/components/SkeletonLoader.js
class SkeletonLoader {
    constructor() {
        this.skeletons = new Map();
        this.observers = new Map();
        this.animationDuration = 300;
        
        this.initializeSkeletonSystem();
    }
    
    initializeSkeletonSystem() {
        // Register skeleton types
        this.registerSkeletonTypes();
        
        // Setup mutation observer for dynamic skeletons
        this.setupMutationObserver();
        
        // Initialize existing skeletons
        this.initializeExistingSkeletons();
    }
    
    registerSkeletonTypes() {
        this.skeletonTypes = {
            'job-card': {
                template: this.getJobCardSkeleton,
                dimensions: { width: '100%', height: '200px' },
                responsive: true
            },
            'job-list': {
                template: this.getJobListSkeleton,
                dimensions: { width: '100%', height: 'auto' },
                count: 10
            },
            'company-card': {
                template: this.getCompanyCardSkeleton,
                dimensions: { width: '100%', height: '150px' },
                responsive: true
            },
            'search-results': {
                template: this.getSearchResultsSkeleton,
                dimensions: { width: '100%', height: 'auto' },
                count: 20
            },
            'ad-banner': {
                template: this.getAdBannerSkeleton,
                dimensions: { width: '728px', height: '90px' },
                responsive: true,
                breakpoints: {
                    mobile: { width: '320px', height: '50px' },
                    tablet: { width: '728px', height: '90px' },
                    desktop: { width: '728px', height: '90px' }
                }
            },
            'ad-rectangle': {
                template: this.getAdRectangleSkeleton,
                dimensions: { width: '300px', height: '250px' },
                responsive: false
            },
            'ad-skyscraper': {
                template: this.getAdSkyscraperSkeleton,
                dimensions: { width: '300px', height: '600px' },
                responsive: false
            },
            'pagination': {
                template: this.getPaginationSkeleton,
                dimensions: { width: '100%', height: '50px' }
            },
            'breadcrumbs': {
                template: this.getBreadcrumbsSkeleton,
                dimensions: { width: '100%', height: '30px' }
            }
        };
    }
    
    createSkeleton(type, container, options = {}) {
        const skeletonConfig = this.skeletonTypes[type];
        if (!skeletonConfig) {
            console.warn(`Skeleton type "${type}" not found`);
            return null;
        }
        
        const skeletonElement = this.buildSkeletonElement(skeletonConfig, options);
        
        // Set dimensions to prevent layout shift
        this.setSkeletonDimensions(skeletonElement, skeletonConfig, container);
        
        // Add to container
        container.appendChild(skeletonElement);
        
        // Store reference
        const skeletonId = this.generateSkeletonId();
        this.skeletons.set(skeletonId, {
            element: skeletonElement,
            container: container,
            type: type,
            config: skeletonConfig
        });
        
        return skeletonId;
    }
    
    buildSkeletonElement(config, options) {
        const skeleton = document.createElement('div');
        skeleton.className = `skeleton skeleton--${config.type || 'default'}`;
        skeleton.setAttribute('data-skeleton', 'true');
        skeleton.setAttribute('aria-hidden', 'true');
        skeleton.setAttribute('role', 'presentation');
        
        // Add accessibility label
        if (options.label) {
            skeleton.setAttribute('aria-label', `Loading ${options.label}`);
        }
        
        // Generate skeleton content
        if (typeof config.template === 'function') {
            skeleton.innerHTML = config.template(options);
        }
        
        return skeleton;
    }
    
    setSkeletonDimensions(element, config, container) {
        const containerRect = container.getBoundingClientRect();
        const containerWidth = containerRect.width;
        
        let dimensions = config.dimensions;
        
        // Apply responsive dimensions
        if (config.responsive && config.breakpoints) {
            if (containerWidth < 768) {
                dimensions = config.breakpoints.mobile || dimensions;
            } else if (containerWidth < 1024) {
                dimensions = config.breakpoints.tablet || dimensions;
            } else {
                dimensions = config.breakpoints.desktop || dimensions;
            }
        }
        
        // Set dimensions
        if (dimensions.width) {
            element.style.width = dimensions.width;
        }
        if (dimensions.height) {
            element.style.height = dimensions.height;
            element.style.minHeight = dimensions.height;
        }
        
        // Ensure no layout shift
        element.style.boxSizing = 'border-box';
    }
    
    getJobCardSkeleton(options = {}) {
        return `
            <div class="skeleton-job-card">
                <div class="skeleton-job-card__header">
                    <div class="skeleton skeleton--title" style="width: 70%; height: 24px; margin-bottom: 8px;"></div>
                    <div class="skeleton skeleton--text" style="width: 50%; height: 16px; margin-bottom: 12px;"></div>
                </div>
                <div class="skeleton-job-card__meta">
                    <div class="skeleton skeleton--text" style="width: 30%; height: 14px; margin-bottom: 4px;"></div>
                    <div class="skeleton skeleton--text" style="width: 40%; height: 14px; margin-bottom: 4px;"></div>
                    <div class="skeleton skeleton--text" style="width: 25%; height: 14px; margin-bottom: 12px;"></div>
                </div>
                <div class="skeleton-job-card__description">
                    <div class="skeleton skeleton--text" style="width: 100%; height: 14px; margin-bottom: 4px;"></div>
                    <div class="skeleton skeleton--text" style="width: 90%; height: 14px; margin-bottom: 4px;"></div>
                    <div class="skeleton skeleton--text" style="width: 75%; height: 14px; margin-bottom: 12px;"></div>
                </div>
                <div class="skeleton-job-card__footer">
                    <div class="skeleton-job-card__tags">
                        <div class="skeleton skeleton--tag" style="width: 60px; height: 20px; margin-right: 8px;"></div>
                        <div class="skeleton skeleton--tag" style="width: 80px; height: 20px; margin-right: 8px;"></div>
                        <div class="skeleton skeleton--tag" style="width: 70px; height: 20px;"></div>
                    </div>
                    <div class="skeleton skeleton--button" style="width: 100px; height: 36px; margin-top: 12px;"></div>
                </div>
            </div>
        `;
    }
    
    getJobListSkeleton(options = {}) {
        const count = options.count || 10;
        const cards = Array.from({ length: count }, (_, i) => 
            `<div class="skeleton-job-list__item">${this.getJobCardSkeleton()}</div>`
        ).join('');
        
        return `
            <div class="skeleton-job-list">
                ${cards}
            </div>
        `;
    }
    
    getAdBannerSkeleton(options = {}) {
        const slotName = options.slot || 'banner';
        return `
            <div class="skeleton-ad skeleton-ad--banner" data-ad-type="banner">
                <div class="skeleton-ad__content">
                    <div class="skeleton-ad__label">Advertisement</div>
                    <div class="skeleton-ad__placeholder">
                        <div class="skeleton skeleton--text" style="width: 60%; height: 16px; margin-bottom: 8px;"></div>
                        <div class="skeleton skeleton--text" style="width: 40%; height: 14px;"></div>
                    </div>
                </div>
            </div>
        `;
    }
    
    getAdRectangleSkeleton(options = {}) {
        return `
            <div class="skeleton-ad skeleton-ad--rectangle" data-ad-type="rectangle">
                <div class="skeleton-ad__content">
                    <div class="skeleton-ad__label">Advertisement</div>
                    <div class="skeleton-ad__placeholder">
                        <div class="skeleton skeleton--image" style="width: 80%; height: 60%; margin-bottom: 12px;"></div>
                        <div class="skeleton skeleton--text" style="width: 70%; height: 16px; margin-bottom: 8px;"></div>
                        <div class="skeleton skeleton--text" style="width: 50%; height: 14px;"></div>
                    </div>
                </div>
            </div>
        `;
    }
    
    getSearchResultsSkeleton(options = {}) {
        const count = options.count || 20;
        const results = Array.from({ length: count }, (_, i) => 
            `<div class="skeleton-search-result">
                <div class="skeleton skeleton--title" style="width: 80%; height: 20px; margin-bottom: 8px;"></div>
                <div class="skeleton skeleton--text" style="width: 60%; height: 14px; margin-bottom: 4px;"></div>
                <div class="skeleton skeleton--text" style="width: 100%; height: 14px; margin-bottom: 4px;"></div>
                <div class="skeleton skeleton--text" style="width: 70%; height: 14px;"></div>
            </div>`
        ).join('');
        
        return `
            <div class="skeleton-search-results">
                ${results}
            </div>
        `;
    }
    
    async replaceSkeleton(skeletonId, content) {
        const skeleton = this.skeletons.get(skeletonId);
        if (!skeleton) return;
        
        const { element, container } = skeleton;
        
        // Create content element
        const contentElement = document.createElement('div');
        contentElement.innerHTML = content;
        
        // Fade out skeleton
        element.style.transition = `opacity ${this.animationDuration}ms ease-out, transform ${this.animationDuration}ms ease-out`;
        element.style.opacity = '0';
        element.style.transform = 'scale(0.95)';
        
        // Wait for fade out
        await this.delay(this.animationDuration);
        
        // Replace skeleton with content
        container.removeChild(element);
        
        // Add content with fade in
        const contentNodes = Array.from(contentElement.children);
        contentNodes.forEach(node => {
            node.style.opacity = '0';
            node.style.transform = 'translateY(10px)';
            container.appendChild(node);
        });
        
        // Trigger reflow
        container.offsetHeight;
        
        // Fade in content
        contentNodes.forEach((node, index) => {
            setTimeout(() => {
                node.style.transition = `opacity ${this.animationDuration}ms ease-out, transform ${this.animationDuration}ms ease-out`;
                node.style.opacity = '1';
                node.style.transform = 'translateY(0)';
            }, index * 50); // Stagger animation
        });
        
        // Clean up
        this.skeletons.delete(skeletonId);
    }
    
    showSkeleton(container, type, options = {}) {
        // Remove existing content
        const existingContent = container.querySelectorAll(':not([data-skeleton])');
        existingContent.forEach(element => {
            element.style.display = 'none';
        });
        
        // Create and show skeleton
        return this.createSkeleton(type, container, options);
    }
    
    hideSkeleton(skeletonId) {
        const skeleton = this.skeletons.get(skeletonId);
        if (!skeleton) return;
        
        const { element, container } = skeleton;
        
        // Show original content
        const hiddenContent = container.querySelectorAll('[style*="display: none"]');
        hiddenContent.forEach(element => {
            element.style.display = '';
        });
        
        // Remove skeleton
        if (element.parentNode) {
            element.parentNode.removeChild(element);
        }
        
        this.skeletons.delete(skeletonId);
    }
    
    setupMutationObserver() {
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Auto-initialize skeletons with data attributes
                        const skeletonElements = node.querySelectorAll('[data-skeleton-type]');
                        skeletonElements.forEach(element => {
                            const type = element.dataset.skeletonType;
                            const options = JSON.parse(element.dataset.skeletonOptions || '{}');
                            this.createSkeleton(type, element, options);
                        });
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        this.observers.set('mutation', observer);
    }
    
    initializeExistingSkeletons() {
        document.querySelectorAll('[data-skeleton-type]').forEach(element => {
            const type = element.dataset.skeletonType;
            const options = JSON.parse(element.dataset.skeletonOptions || '{}');
            this.createSkeleton(type, element, options);
        });
    }
    
    generateSkeletonId() {
        return 'skeleton_' + Math.random().toString(36).substr(2, 9);
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    destroy() {
        // Clean up all skeletons
        this.skeletons.forEach((skeleton, id) => {
            this.hideSkeleton(id);
        });
        
        // Disconnect observers
        this.observers.forEach(observer => {
            observer.disconnect();
        });
        
        this.skeletons.clear();
        this.observers.clear();
    }
}

// Global skeleton loader instance
const skeletonLoader = new SkeletonLoader();

// Export for use in other modules
export default skeletonLoader;
```

## CSS Skeleton Styles

### Base Skeleton CSS (included in main bundle)
```css
/* Base skeleton styles in main CSS bundle */
@layer base {
    :root {
        --skeleton-color: #f0f0f0;
        --skeleton-highlight: #e0e0e0;
        --skeleton-animation-duration: 1.5s;
        --skeleton-border-radius: 4px;
        
        /* Ad-specific skeleton colors */
        --skeleton-ad-color: #fff3e0;
        --skeleton-ad-highlight: #ffe0b2;
        --skeleton-ad-border: #ffcc02;
    }
    
    @media (prefers-color-scheme: dark) {
        :root {
            --skeleton-color: #2a2a2a;
            --skeleton-highlight: #3a3a3a;
            --skeleton-ad-color: #2d2416;
            --skeleton-ad-highlight: #3d3426;
            --skeleton-ad-border: #cc9902;
        }
    }
}

@layer components {
    .skeleton {
        background: linear-gradient(
            90deg,
            var(--skeleton-color) 25%,
            var(--skeleton-highlight) 50%,
            var(--skeleton-color) 75%
        );
        background-size: 200% 100%;
        animation: skeleton-shimmer var(--skeleton-animation-duration) infinite;
        border-radius: var(--skeleton-border-radius);
        position: relative;
        overflow: hidden;
        
        /* Prevent text selection */
        user-select: none;
        pointer-events: none;
        
        /* Accessibility */
        speak: none;
    }
    
    /* Skeleton variants */
    .skeleton--text {
        height: 1em;
        margin-bottom: 0.5em;
    }
    
    .skeleton--title {
        height: 1.5em;
        margin-bottom: 1em;
    }
    
    .skeleton--button {
        border-radius: 6px;
        height: 36px;
    }
    
    .skeleton--tag {
        border-radius: 12px;
        height: 20px;
        display: inline-block;
        margin-right: 8px;
    }
    
    .skeleton--image {
        aspect-ratio: 16/9;
        width: 100%;
    }
    
    /* Ad-specific skeletons */
    .skeleton-ad {
        background: linear-gradient(
            90deg,
            var(--skeleton-ad-color) 25%,
            var(--skeleton-ad-highlight) 50%,
            var(--skeleton-ad-color) 75%
        );
        background-size: 200% 100%;
        border: 1px solid var(--skeleton-ad-border);
        border-radius: var(--skeleton-border-radius);
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: skeleton-shimmer var(--skeleton-animation-duration) infinite;
    }
    
    .skeleton-ad__label {
        position: absolute;
        top: 8px;
        right: 8px;
        font-size: 10px;
        color: #999;
        background: rgba(255, 255, 255, 0.8);
        padding: 2px 6px;
        border-radius: 2px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .skeleton-ad--banner {
        width: 100%;
        max-width: 728px;
        height: 90px;
        margin: 0 auto;
    }
    
    .skeleton-ad--rectangle {
        width: 300px;
        height: 250px;
    }
    
    .skeleton-ad--skyscraper {
        width: 300px;
        height: 600px;
    }
    
    /* Job card skeleton */
    .skeleton-job-card {
        padding: 20px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        margin-bottom: 16px;
        background: #fff;
    }
    
    .skeleton-job-card__header {
        margin-bottom: 16px;
    }
    
    .skeleton-job-card__meta {
        margin-bottom: 16px;
    }
    
    .skeleton-job-card__description {
        margin-bottom: 16px;
    }
    
    .skeleton-job-card__footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .skeleton-job-card__tags {
        display: flex;
        align-items: center;
    }
    
    /* Responsive skeleton adjustments */
    @container (max-width: 768px) {
        .skeleton-ad--banner {
            max-width: 320px;
            height: 50px;
        }
        
        .skeleton-job-card {
            padding: 16px;
        }
    }
    
    /* Animation */
    @keyframes skeleton-shimmer {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    
    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .skeleton {
            animation: none;
            background: var(--skeleton-color);
        }
        
        .skeleton-ad {
            animation: none;
            background: var(--skeleton-ad-color);
        }
    }
    
    /* High contrast mode */
    @media (prefers-contrast: high) {
        .skeleton {
            background: #ddd;
            border: 1px solid #999;
        }
        
        .skeleton-ad {
            background: #f5f5f5;
            border: 2px solid #333;
        }
    }
}
```

## Usage Examples

### Dynamic Content Loading with Skeletons
```javascript
// Example: Loading job listings with skeleton
async function loadJobListings(location, jobTitle) {
    const container = document.getElementById('job-listings');
    
    // Show skeleton while loading
    const skeletonId = skeletonLoader.showSkeleton(container, 'job-list', {
        count: 20,
        label: `${jobTitle} jobs in ${location}`
    });
    
    try {
        // Fetch data
        const response = await fetch(`/api/locations/${location}/${jobTitle}.json`);
        const data = await response.json();
        
        // Generate job cards HTML
        const jobsHTML = data.jobs.map(job => generateJobCardHTML(job)).join('');
        
        // Replace skeleton with content
        await skeletonLoader.replaceSkeleton(skeletonId, jobsHTML);
        
        // Initialize dynamic components
        initializeDynamicComponents(container);
        
    } catch (error) {
        // Handle error - show error message
        skeletonLoader.replaceSkeleton(skeletonId, '<div class="error">Failed to load jobs</div>');
    }
}

// Example: AdSense loading with skeleton
function loadAdSenseAd(container, slotId) {
    // Create ad skeleton
    const skeletonId = skeletonLoader.createSkeleton('ad-banner', container, {
        slot: slotId,
        label: 'Advertisement'
    });
    
    // Load AdSense
    const adComponent = new AdSenseComponent(container, {
        clientId: 'ca-pub-xxxxxxxxxxxxxxxx',
        slotId: slotId,
        onLoad: () => {
            // Remove skeleton when ad loads
            skeletonLoader.hideSkeleton(skeletonId);
        },
        onError: () => {
            // Keep skeleton or show fallback
            skeletonLoader.replaceSkeleton(skeletonId, '<div class="ad-fallback">Advertisement</div>');
        }
    });
}
```

This advanced skeleton loading system ensures:

1. **Zero Layout Shift** - Exact dimensions preserved
2. **Responsive Design** - Adapts to different screen sizes
3. **Accessibility** - Proper ARIA labels and reduced motion support
4. **Performance** - Lightweight and efficient animations
5. **AdSense Integration** - Specific skeletons for ad placements
6. **Smooth Transitions** - Elegant fade in/out animations
7. **Error Handling** - Graceful fallbacks for failed loads

The system prevents any visual jumping or layout shifts while providing an excellent user experience during content loading! 🚀
