# Development Guidelines and Coding Standards

## File Organization Rules

### Universal File Constraints
- **Maximum 150 lines per file** (strictly enforced)
- **Single Responsibility Principle** - One concern per file
- **Clear naming conventions** with descriptive names
- **Proper namespace organization** for all components
- **Comprehensive documentation** in each file

### Directory Structure Rules
```
src/
├── [Feature]/                         (Feature-based organization)
│   ├── [Component]/                   (Component grouping)
│   │   ├── Component.php/.tsx/.css    (Implementation file)
│   │   ├── Component.test.php/.tsx    (Test file)
│   │   ├── Component.stories.tsx      (Storybook stories - React only)
│   │   └── index.php/.ts              (Export/barrel file)
│   └── types/                         (Type definitions)
```

## PHP 8+ Advanced Coding Standards

### Namespace and Class Structure
```php
<?php

declare(strict_types=1);

namespace UniversalApp\Theme\Services;

use UniversalApp\Theme\Interfaces\ServiceInterface;
use UniversalApp\Theme\Traits\Configurable;
use UniversalApp\Theme\Enums\ServiceStatus;
use UniversalApp\Theme\Exceptions\ServiceException;

/**
 * Template rendering service with advanced PHP 8+ features
 * 
 * @package UniversalApp\Theme\Services
 * <AUTHOR> Team
 * @since   1.0.0
 */
final readonly class TemplateService implements ServiceInterface
{
    use Configurable;

    public function __construct(
        private string $templatePath,
        private array $globalVariables = [],
        private ServiceStatus $status = ServiceStatus::ACTIVE
    ) {}

    #[Route('/api/template/{template}')]
    #[Cache(ttl: 3600)]
    public function render(string $template, array $variables = []): string
    {
        return match ($this->status) {
            ServiceStatus::ACTIVE => $this->processTemplate($template, $variables),
            ServiceStatus::MAINTENANCE => throw new ServiceException('Service in maintenance'),
            default => throw new ServiceException('Invalid service status')
        };
    }

    private function processTemplate(string $template, array $variables): string
    {
        $templateFile = $this->templatePath . '/' . $template . '.php';
        
        if (!file_exists($templateFile)) {
            throw new ServiceException("Template not found: {$template}");
        }

        return $this->renderWithIsolation($templateFile, [
            ...$this->globalVariables,
            ...$variables
        ]);
    }

    private function renderWithIsolation(string $file, array $vars): string
    {
        return (static function() use ($file, $vars): string {
            extract($vars, EXTR_SKIP);
            ob_start();
            include $file;
            return ob_get_clean() ?: '';
        })();
    }
}
```

### Enum Implementation
```php
<?php

declare(strict_types=1);

namespace UniversalApp\Plugin\Enums;

/**
 * Job status enumeration with advanced features
 */
enum JobStatus: string
{
    case DRAFT = 'draft';
    case PUBLISHED = 'published';
    case EXPIRED = 'expired';
    case ARCHIVED = 'archived';

    public function label(): string
    {
        return match ($this) {
            self::DRAFT => 'Draft',
            self::PUBLISHED => 'Published',
            self::EXPIRED => 'Expired',
            self::ARCHIVED => 'Archived'
        };
    }

    public function isActive(): bool
    {
        return $this === self::PUBLISHED;
    }

    public function canBeEdited(): bool
    {
        return in_array($this, [self::DRAFT, self::PUBLISHED], true);
    }

    public static function getActiveStatuses(): array
    {
        return array_filter(
            self::cases(),
            static fn(self $status): bool => $status->isActive()
        );
    }
}
```

### Trait Implementation
```php
<?php

declare(strict_types=1);

namespace UniversalApp\Theme\Traits;

use Psr\Log\LoggerInterface;
use UniversalApp\Theme\Exceptions\CacheException;

/**
 * Advanced caching functionality trait
 */
trait Cacheable
{
    private ?LoggerInterface $logger = null;
    private array $cacheConfig = [];

    public function setCacheConfig(array $config): void
    {
        $this->cacheConfig = $config;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    protected function cache(string $key, callable $callback, int $ttl = 3600): mixed
    {
        $cacheKey = $this->generateCacheKey($key);
        
        // Try to get from cache
        $cached = $this->getFromCache($cacheKey);
        if ($cached !== null) {
            $this->logger?->debug("Cache hit for key: {$cacheKey}");
            return $cached;
        }

        // Generate new value
        try {
            $value = $callback();
            $this->storeInCache($cacheKey, $value, $ttl);
            $this->logger?->debug("Cache stored for key: {$cacheKey}");
            return $value;
        } catch (\Throwable $e) {
            $this->logger?->error("Cache generation failed: {$e->getMessage()}");
            throw new CacheException("Failed to generate cached value", 0, $e);
        }
    }

    private function generateCacheKey(string $key): string
    {
        $prefix = $this->cacheConfig['prefix'] ?? 'universal_app';
        return sprintf('%s:%s:%s', $prefix, static::class, $key);
    }

    private function getFromCache(string $key): mixed
    {
        return wp_cache_get($key, 'universal_app');
    }

    private function storeInCache(string $key, mixed $value, int $ttl): void
    {
        wp_cache_set($key, $value, 'universal_app', $ttl);
    }
}
```

## CSS Advanced Standards

### CSS Custom Properties System
```css
/* Base CSS with advanced custom properties */
:root {
  /* Color system with HSL for better manipulation */
  --color-primary-h: 220;
  --color-primary-s: 90%;
  --color-primary-l: 50%;
  --color-primary: hsl(var(--color-primary-h) var(--color-primary-s) var(--color-primary-l));
  --color-primary-alpha: hsl(var(--color-primary-h) var(--color-primary-s) var(--color-primary-l) / 0.1);
  
  /* Fluid typography using clamp() */
  --text-xs: clamp(0.7rem, 0.66rem + 0.2vw, 0.75rem);
  --text-sm: clamp(0.8rem, 0.74rem + 0.3vw, 0.875rem);
  --text-base: clamp(0.9rem, 0.83rem + 0.35vw, 1rem);
  --text-lg: clamp(1rem, 0.91rem + 0.45vw, 1.125rem);
  
  /* Spacing system with consistent ratios */
  --space-3xs: clamp(0.25rem, 0.2rem + 0.25vw, 0.375rem);
  --space-2xs: clamp(0.5rem, 0.4rem + 0.5vw, 0.75rem);
  --space-xs: clamp(0.75rem, 0.6rem + 0.75vw, 1rem);
  --space-sm: clamp(1rem, 0.8rem + 1vw, 1.5rem);
  
  /* Container queries breakpoints */
  --container-xs: 20rem;
  --container-sm: 24rem;
  --container-md: 28rem;
  --container-lg: 32rem;
  --container-xl: 36rem;
}

/* Dark mode with logical properties */
@media (prefers-color-scheme: dark) {
  :root {
    --color-primary-l: 60%;
    --color-background: hsl(220 15% 8%);
    --color-surface: hsl(220 15% 12%);
    --color-text: hsl(220 15% 95%);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-primary: hsl(220 100% 40%);
    --color-text: hsl(0 0% 0%);
    --color-background: hsl(0 0% 100%);
  }
}
```

### Advanced CSS Grid with Subgrid
```css
/* Advanced grid system with subgrid support */
.grid-system {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: var(--space-md);
  container-type: inline-size;
}

.grid-item {
  display: grid;
  grid-template-rows: subgrid;
  grid-row: span 3;
}

/* Container queries for responsive components */
@container (min-width: 400px) {
  .job-card {
    display: grid;
    grid-template-columns: auto 1fr auto;
    grid-template-areas: 
      "logo title actions"
      "logo meta actions"
      "description description description";
    gap: var(--space-sm);
  }
}

@container (min-width: 600px) {
  .job-card {
    grid-template-areas: 
      "logo title actions"
      "logo meta meta"
      "description description description";
  }
}

/* CSS Layers for cascade control */
@layer reset, base, components, utilities, overrides;

@layer reset {
  *, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }
}

@layer base {
  body {
    font-family: var(--font-primary);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    color: var(--color-text);
    background-color: var(--color-background);
  }
}

@layer components {
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding-block: var(--space-xs);
    padding-inline: var(--space-sm);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:where(:hover, :focus-visible) {
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}
```

## JavaScript/TypeScript Advanced Standards

### Modern ES6+ Class Implementation
```typescript
// Advanced TypeScript class with decorators and modern features
import { debounce, throttle, memoize } from '../utils/performance';
import { EventEmitter } from '../utils/events';

interface SearchOptions {
  debounceMs?: number;
  minQueryLength?: number;
  maxResults?: number;
}

interface SearchResult {
  id: string;
  title: string;
  type: 'job' | 'company' | 'category';
  url: string;
  score: number;
}

/**
 * Advanced search service with performance optimizations
 */
export class SearchService extends EventEmitter {
  private readonly options: Required<SearchOptions>;
  private searchIndex: Map<string, SearchResult[]> = new Map();
  private abortController?: AbortController;

  constructor(options: SearchOptions = {}) {
    super();
    this.options = {
      debounceMs: 300,
      minQueryLength: 2,
      maxResults: 10,
      ...options
    };
    
    // Bind methods to preserve context
    this.search = debounce(this.search.bind(this), this.options.debounceMs);
    this.updateIndex = throttle(this.updateIndex.bind(this), 5000);
  }

  /**
   * Perform search with advanced features
   */
  public async search(query: string): Promise<SearchResult[]> {
    if (query.length < this.options.minQueryLength) {
      return [];
    }

    // Cancel previous request
    this.abortController?.abort();
    this.abortController = new AbortController();

    try {
      this.emit('searchStart', { query });
      
      const results = await this.performSearch(query, {
        signal: this.abortController.signal
      });
      
      this.emit('searchComplete', { query, results });
      return results;
      
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        this.emit('searchError', { query, error });
        throw error;
      }
      return [];
    }
  }

  /**
   * Memoized search implementation
   */
  @memoize({ maxSize: 100, ttl: 300000 }) // 5 minutes TTL
  private async performSearch(
    query: string, 
    options: { signal: AbortSignal }
  ): Promise<SearchResult[]> {
    const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`, {
      signal: options.signal,
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`Search failed: ${response.statusText}`);
    }

    const data = await response.json();
    return this.processResults(data.results);
  }

  /**
   * Process and rank search results
   */
  private processResults(results: any[]): SearchResult[] {
    return results
      .map(this.transformResult)
      .sort((a, b) => b.score - a.score)
      .slice(0, this.options.maxResults);
  }

  /**
   * Transform raw result to SearchResult interface
   */
  private transformResult = (result: any): SearchResult => ({
    id: result.id,
    title: result.title,
    type: result.type,
    url: result.url,
    score: this.calculateRelevanceScore(result)
  });

  /**
   * Calculate relevance score using multiple factors
   */
  private calculateRelevanceScore(result: any): number {
    let score = 0;
    
    // Title match bonus
    if (result.title_match) score += 10;
    
    // Recency bonus
    const daysSincePosted = result.days_since_posted || 0;
    score += Math.max(0, 5 - daysSincePosted * 0.1);
    
    // Type-specific bonuses
    const typeBonus = { job: 3, company: 2, category: 1 };
    score += typeBonus[result.type as keyof typeof typeBonus] || 0;
    
    return score;
  }

  /**
   * Update search index with new data
   */
  private async updateIndex(): Promise<void> {
    try {
      const response = await fetch('/api/search/index');
      const indexData = await response.json();
      
      this.searchIndex.clear();
      for (const [key, results] of Object.entries(indexData)) {
        this.searchIndex.set(key, results as SearchResult[]);
      }
      
      this.emit('indexUpdated', { size: this.searchIndex.size });
    } catch (error) {
      this.emit('indexUpdateError', { error });
    }
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    this.abortController?.abort();
    this.searchIndex.clear();
    this.removeAllListeners();
  }
}

// Decorator for memoization
function memoize(options: { maxSize: number; ttl: number }) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const cache = new Map();
    
    descriptor.value = function (...args: any[]) {
      const key = JSON.stringify(args);
      const cached = cache.get(key);
      
      if (cached && Date.now() - cached.timestamp < options.ttl) {
        return cached.value;
      }
      
      const result = originalMethod.apply(this, args);
      
      // Manage cache size
      if (cache.size >= options.maxSize) {
        const firstKey = cache.keys().next().value;
        cache.delete(firstKey);
      }
      
      cache.set(key, { value: result, timestamp: Date.now() });
      return result;
    };
  };
}
```

### Performance Utilities
```typescript
// Performance optimization utilities
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(this, args), wait);
  };
};

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

export const memoize = <T extends (...args: any[]) => any>(
  func: T,
  options: { maxSize?: number; ttl?: number } = {}
): T => {
  const cache = new Map();
  const { maxSize = 100, ttl = Infinity } = options;
  
  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = JSON.stringify(args);
    const cached = cache.get(key);
    
    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.value;
    }
    
    const result = func(...args);
    
    if (cache.size >= maxSize) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }
    
    cache.set(key, { value: result, timestamp: Date.now() });
    return result;
  }) as T;
};
```

## Code Quality Standards

### Linting and Formatting
```json
// .eslintrc.js
{
  "extends": [
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended"
  ],
  "rules": {
    "max-lines": ["error", 150],
    "max-lines-per-function": ["error", 50],
    "complexity": ["error", 10],
    "@typescript-eslint/no-unused-vars": "error",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

### Testing Standards
```typescript
// Component test example
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SearchWidget } from './SearchWidget';

describe('SearchWidget', () => {
  it('should debounce search input', async () => {
    const mockSearch = jest.fn();
    render(<SearchWidget onSearch={mockSearch} />);
    
    const input = screen.getByRole('searchbox');
    
    fireEvent.change(input, { target: { value: 'test' } });
    fireEvent.change(input, { target: { value: 'test query' } });
    
    // Should not call immediately
    expect(mockSearch).not.toHaveBeenCalled();
    
    // Should call after debounce delay
    await waitFor(() => {
      expect(mockSearch).toHaveBeenCalledWith('test query');
    }, { timeout: 500 });
    
    expect(mockSearch).toHaveBeenCalledTimes(1);
  });
});
```

## Documentation Standards

### Code Documentation
- **PHPDoc/JSDoc** for all public methods
- **Type annotations** for all parameters and return values
- **Usage examples** in complex functions
- **Performance notes** for optimization-critical code
- **Security considerations** for user input handling

### File Header Template
```php
<?php
/**
 * Brief description of the file's purpose
 *
 * Longer description explaining the file's role in the system,
 * any important implementation details, and usage patterns.
 *
 * @package    UniversalApp\Theme\Services
 * @subpackage TemplateEngine
 * <AUTHOR> Team <<EMAIL>>
 * @copyright  2024 Universal App
 * @license    GPL-2.0-or-later
 * @since      1.0.0
 * @version    1.2.0
 *
 * @see        https://docs.universalapp.com/template-service
 * @link       https://github.com/universalapp/theme
 */
```
