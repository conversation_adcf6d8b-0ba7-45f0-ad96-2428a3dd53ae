# Stage 13: Plugin Foundation & Structure

## Stage Overview
Create the foundational WordPress plugin structure for the Universal Jobs plugin, implementing custom post types, REST API endpoints, and admin interfaces following the modular architecture.

## Prerequisites
- Stages 1-12 completed successfully
- WordPress theme foundation working
- Component system operational
- Static site generation working

## Implementation Steps

### Step 13.1: Create Plugin Header Files

#### manager/wp-content/plugins/universal-jobs/universal-jobs.php
```php
<?php
/**
 * Plugin Name: Universal Jobs
 * Plugin URI: https://universal-app.com/plugins/universal-jobs
 * Description: Universal job management system with headless capabilities
 * Version: 1.0.0
 * Author: Universal App Team
 * Author URI: https://universal-app.com
 * Text Domain: universal-jobs
 * Domain Path: /languages
 * Requires at least: 6.4
 * Tested up to: 6.4
 * Requires PHP: 8.2
 * License: GPL v2 or later
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('UNIVERSAL_JOBS_VERSION', '1.0.0');
define('UNIVERSAL_JOBS_PLUGIN_FILE', __FILE__);
define('UNIVERSAL_JOBS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('UNIVERSAL_JOBS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('UNIVERSAL_JOBS_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Autoload classes
require_once UNIVERSAL_JOBS_PLUGIN_DIR . 'vendor/autoload.php';

// Initialize plugin
use UniversalApp\Plugin\Core\PluginBootstrap;

try {
    $plugin = new PluginBootstrap();
    $plugin->init();
} catch (Exception $e) {
    error_log('Universal Jobs Plugin Error: ' . $e->getMessage());
    
    // Show admin notice for errors
    if (is_admin()) {
        add_action('admin_notices', function() use ($e) {
            echo '<div class="notice notice-error"><p>Universal Jobs Plugin Error: ' . esc_html($e->getMessage()) . '</p></div>';
        });
    }
}

// Activation and deactivation hooks
register_activation_hook(__FILE__, [PluginBootstrap::class, 'activate']);
register_deactivation_hook(__FILE__, [PluginBootstrap::class, 'deactivate']);
```

#### manager/wp-content/plugins/universal-jobs/composer.json
```json
{
    "name": "universal-app/jobs-plugin",
    "type": "wordpress-plugin",
    "description": "Universal Jobs WordPress Plugin",
    "require": {
        "php": ">=8.2",
        "psr/container": "^2.0",
        "psr/log": "^3.0"
    },
    "require-dev": {
        "phpunit/phpunit": "^10.5",
        "phpstan/phpstan": "^1.10"
    },
    "autoload": {
        "psr-4": {
            "UniversalApp\\Plugin\\": "src/"
        }
    },
    "autoload-dev": {
        "psr-4": {
            "UniversalApp\\Plugin\\Tests\\": "tests/"
        }
    }
}
```

### Step 13.2: Create Plugin Bootstrap Class

#### manager/wp-content/plugins/universal-jobs/src/Core/PluginBootstrap.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Plugin\Core;

use UniversalApp\Plugin\Services\JobService;
use UniversalApp\Plugin\Services\CompanyService;
use UniversalApp\Plugin\Services\ApiService;
use UniversalApp\Plugin\Admin\JobAdmin;
use UniversalApp\Plugin\Admin\CompanyAdmin;

/**
 * Plugin Bootstrap Class
 * 
 * Initializes the plugin and sets up core functionality
 * 
 * @package UniversalApp\Plugin\Core
 * @since 1.0.0
 */
final class PluginBootstrap
{
    private Container $container;
    private bool $initialized = false;

    public function __construct()
    {
        $this->container = new Container();
        $this->registerServices();
    }

    /**
     * Initialize the plugin
     */
    public function init(): void
    {
        if ($this->initialized) {
            return;
        }

        // WordPress hooks
        add_action('init', [$this, 'initializePostTypes']);
        add_action('init', [$this, 'initializeTaxonomies']);
        add_action('rest_api_init', [$this, 'registerApiEndpoints']);
        add_action('admin_menu', [$this, 'addAdminMenus']);
        add_action('admin_init', [$this, 'registerSettings']);

        // Plugin-specific hooks
        add_action('save_post', [$this, 'handlePostSave'], 10, 2);
        add_action('delete_post', [$this, 'handlePostDelete']);

        // Load text domain
        add_action('plugins_loaded', [$this, 'loadTextDomain']);

        $this->initialized = true;
    }

    /**
     * Initialize custom post types
     */
    public function initializePostTypes(): void
    {
        $jobService = $this->container->get(JobService::class);
        $jobService->registerPostType();

        $companyService = $this->container->get(CompanyService::class);
        $companyService->registerPostType();
    }

    /**
     * Initialize taxonomies
     */
    public function initializeTaxonomies(): void
    {
        $jobService = $this->container->get(JobService::class);
        $jobService->registerTaxonomies();
    }

    /**
     * Register REST API endpoints
     */
    public function registerApiEndpoints(): void
    {
        $apiService = $this->container->get(ApiService::class);
        $apiService->registerEndpoints();
    }

    /**
     * Add admin menus
     */
    public function addAdminMenus(): void
    {
        add_menu_page(
            __('Universal Jobs', 'universal-jobs'),
            __('Universal Jobs', 'universal-jobs'),
            'manage_options',
            'universal-jobs',
            [$this, 'renderMainAdminPage'],
            'dashicons-businessman',
            30
        );

        // Initialize admin classes
        new JobAdmin($this->container->get(JobService::class));
        new CompanyAdmin($this->container->get(CompanyService::class));
    }

    /**
     * Register plugin settings
     */
    public function registerSettings(): void
    {
        register_setting('universal_jobs_settings', 'universal_jobs_api_enabled');
        register_setting('universal_jobs_settings', 'universal_jobs_auto_generate');
        register_setting('universal_jobs_settings', 'universal_jobs_cache_duration');
    }

    /**
     * Handle post save
     */
    public function handlePostSave(int $postId, \WP_Post $post): void
    {
        if (!in_array($post->post_type, ['job', 'company'])) {
            return;
        }

        if ($post->post_status !== 'publish') {
            return;
        }

        // Trigger static site regeneration
        $this->triggerStaticRegeneration($postId, $post->post_type);
    }

    /**
     * Handle post deletion
     */
    public function handlePostDelete(int $postId): void
    {
        $post = get_post($postId);
        
        if (!$post || !in_array($post->post_type, ['job', 'company'])) {
            return;
        }

        // Trigger static site regeneration
        $this->triggerStaticRegeneration($postId, $post->post_type, 'delete');
    }

    /**
     * Load text domain
     */
    public function loadTextDomain(): void
    {
        load_plugin_textdomain(
            'universal-jobs',
            false,
            dirname(UNIVERSAL_JOBS_PLUGIN_BASENAME) . '/languages'
        );
    }

    /**
     * Render main admin page
     */
    public function renderMainAdminPage(): void
    {
        include UNIVERSAL_JOBS_PLUGIN_DIR . 'templates/admin/main-page.php';
    }

    /**
     * Plugin activation
     */
    public static function activate(): void
    {
        // Create database tables if needed
        self::createTables();
        
        // Set default options
        add_option('universal_jobs_api_enabled', true);
        add_option('universal_jobs_auto_generate', true);
        add_option('universal_jobs_cache_duration', 3600);
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     */
    public static function deactivate(): void
    {
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Create database tables
     */
    private static function createTables(): void
    {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Job applications table
        $table_name = $wpdb->prefix . 'job_applications';
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            job_id bigint(20) NOT NULL,
            applicant_email varchar(255) NOT NULL,
            applicant_name varchar(255) NOT NULL,
            resume_url varchar(500),
            cover_letter text,
            status varchar(50) DEFAULT 'pending',
            applied_date datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY job_id (job_id),
            KEY applicant_email (applicant_email),
            KEY status (status)
        ) $charset_collate;";

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
    }

    /**
     * Register services in container
     */
    private function registerServices(): void
    {
        $this->container->set(JobService::class, new JobService());
        $this->container->set(CompanyService::class, new CompanyService());
        $this->container->set(ApiService::class, new ApiService());
    }

    /**
     * Trigger static site regeneration
     */
    private function triggerStaticRegeneration(int $postId, string $postType, string $action = 'save'): void
    {
        if (!get_option('universal_jobs_auto_generate', true)) {
            return;
        }

        $data = [
            'post_id' => $postId,
            'post_type' => $postType,
            'action' => $action,
            'timestamp' => time()
        ];

        // Schedule background task
        wp_schedule_single_event(time() + 30, 'universal_jobs_regenerate_static', [$data]);
    }

    /**
     * Get container instance
     */
    public function getContainer(): Container
    {
        return $this->container;
    }
}
```

### Step 13.3: Create Simple DI Container for Plugin

#### manager/wp-content/plugins/universal-jobs/src/Core/Container.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Plugin\Core;

use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;

/**
 * Simple Dependency Injection Container for Plugin
 * 
 * @package UniversalApp\Plugin\Core
 * @since 1.0.0
 */
final class Container implements ContainerInterface
{
    private array $services = [];
    private array $instances = [];

    /**
     * Register a service
     */
    public function set(string $id, mixed $service): void
    {
        $this->services[$id] = $service;
    }

    /**
     * Get a service
     */
    public function get(string $id): mixed
    {
        if (!$this->has($id)) {
            throw new class("Service '{$id}' not found") extends \Exception implements NotFoundExceptionInterface {};
        }

        // Return singleton instance if already created
        if (isset($this->instances[$id])) {
            return $this->instances[$id];
        }

        $service = $this->services[$id];

        // If it's a callable, invoke it
        if (is_callable($service)) {
            $this->instances[$id] = $service($this);
        } else {
            $this->instances[$id] = $service;
        }

        return $this->instances[$id];
    }

    /**
     * Check if service exists
     */
    public function has(string $id): bool
    {
        return isset($this->services[$id]);
    }
}
```

### Step 13.4: Create Job Service

#### manager/wp-content/plugins/universal-jobs/src/Services/JobService.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Plugin\Services;

/**
 * Job Service
 * 
 * Handles job post type registration and management
 * 
 * @package UniversalApp\Plugin\Services
 * @since 1.0.0
 */
final class JobService
{
    /**
     * Register job post type
     */
    public function registerPostType(): void
    {
        $labels = [
            'name' => __('Jobs', 'universal-jobs'),
            'singular_name' => __('Job', 'universal-jobs'),
            'add_new' => __('Add New Job', 'universal-jobs'),
            'add_new_item' => __('Add New Job', 'universal-jobs'),
            'edit_item' => __('Edit Job', 'universal-jobs'),
            'new_item' => __('New Job', 'universal-jobs'),
            'view_item' => __('View Job', 'universal-jobs'),
            'view_items' => __('View Jobs', 'universal-jobs'),
            'search_items' => __('Search Jobs', 'universal-jobs'),
            'not_found' => __('No jobs found', 'universal-jobs'),
            'not_found_in_trash' => __('No jobs found in trash', 'universal-jobs'),
            'all_items' => __('All Jobs', 'universal-jobs'),
            'archives' => __('Job Archives', 'universal-jobs'),
            'attributes' => __('Job Attributes', 'universal-jobs'),
            'insert_into_item' => __('Insert into job', 'universal-jobs'),
            'uploaded_to_this_item' => __('Uploaded to this job', 'universal-jobs'),
        ];

        $args = [
            'labels' => $labels,
            'public' => true,
            'publicly_queryable' => false, // Handled by static site
            'show_ui' => true,
            'show_in_menu' => 'universal-jobs',
            'show_in_nav_menus' => false,
            'show_in_admin_bar' => true,
            'show_in_rest' => true,
            'rest_base' => 'jobs',
            'rest_controller_class' => 'WP_REST_Posts_Controller',
            'menu_position' => null,
            'menu_icon' => 'dashicons-businessman',
            'capability_type' => 'post',
            'capabilities' => [
                'edit_post' => 'edit_job',
                'read_post' => 'read_job',
                'delete_post' => 'delete_job',
                'edit_posts' => 'edit_jobs',
                'edit_others_posts' => 'edit_others_jobs',
                'publish_posts' => 'publish_jobs',
                'read_private_posts' => 'read_private_jobs',
            ],
            'map_meta_cap' => true,
            'hierarchical' => false,
            'supports' => [
                'title',
                'editor',
                'thumbnail',
                'excerpt',
                'custom-fields',
                'revisions',
                'page-attributes'
            ],
            'has_archive' => false, // Handled by static site
            'rewrite' => false, // No WordPress URLs needed
            'query_var' => true,
            'can_export' => true,
            'delete_with_user' => false,
        ];

        register_post_type('job', $args);
    }

    /**
     * Register job taxonomies
     */
    public function registerTaxonomies(): void
    {
        // Job Categories
        register_taxonomy('job_category', 'job', [
            'labels' => [
                'name' => __('Job Categories', 'universal-jobs'),
                'singular_name' => __('Job Category', 'universal-jobs'),
                'add_new_item' => __('Add New Category', 'universal-jobs'),
                'edit_item' => __('Edit Category', 'universal-jobs'),
                'update_item' => __('Update Category', 'universal-jobs'),
                'view_item' => __('View Category', 'universal-jobs'),
                'separate_items_with_commas' => __('Separate categories with commas', 'universal-jobs'),
                'add_or_remove_items' => __('Add or remove categories', 'universal-jobs'),
                'choose_from_most_used' => __('Choose from the most used', 'universal-jobs'),
                'popular_items' => __('Popular Categories', 'universal-jobs'),
                'search_items' => __('Search Categories', 'universal-jobs'),
                'not_found' => __('Not Found', 'universal-jobs'),
            ],
            'hierarchical' => true,
            'public' => true,
            'show_ui' => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => false,
            'show_tagcloud' => false,
            'show_in_rest' => true,
            'rest_base' => 'job-categories',
            'rewrite' => false,
        ]);

        // Job Locations
        register_taxonomy('job_location', 'job', [
            'labels' => [
                'name' => __('Job Locations', 'universal-jobs'),
                'singular_name' => __('Job Location', 'universal-jobs'),
                'add_new_item' => __('Add New Location', 'universal-jobs'),
                'edit_item' => __('Edit Location', 'universal-jobs'),
                'update_item' => __('Update Location', 'universal-jobs'),
                'view_item' => __('View Location', 'universal-jobs'),
                'separate_items_with_commas' => __('Separate locations with commas', 'universal-jobs'),
                'add_or_remove_items' => __('Add or remove locations', 'universal-jobs'),
                'choose_from_most_used' => __('Choose from the most used', 'universal-jobs'),
                'popular_items' => __('Popular Locations', 'universal-jobs'),
                'search_items' => __('Search Locations', 'universal-jobs'),
                'not_found' => __('Not Found', 'universal-jobs'),
            ],
            'hierarchical' => true,
            'public' => true,
            'show_ui' => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => false,
            'show_tagcloud' => false,
            'show_in_rest' => true,
            'rest_base' => 'job-locations',
            'rewrite' => false,
        ]);

        // Job Types
        register_taxonomy('job_type', 'job', [
            'labels' => [
                'name' => __('Job Types', 'universal-jobs'),
                'singular_name' => __('Job Type', 'universal-jobs'),
                'add_new_item' => __('Add New Type', 'universal-jobs'),
                'edit_item' => __('Edit Type', 'universal-jobs'),
                'update_item' => __('Update Type', 'universal-jobs'),
                'view_item' => __('View Type', 'universal-jobs'),
                'separate_items_with_commas' => __('Separate types with commas', 'universal-jobs'),
                'add_or_remove_items' => __('Add or remove types', 'universal-jobs'),
                'choose_from_most_used' => __('Choose from the most used', 'universal-jobs'),
                'popular_items' => __('Popular Types', 'universal-jobs'),
                'search_items' => __('Search Types', 'universal-jobs'),
                'not_found' => __('Not Found', 'universal-jobs'),
            ],
            'hierarchical' => false,
            'public' => true,
            'show_ui' => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => false,
            'show_tagcloud' => true,
            'show_in_rest' => true,
            'rest_base' => 'job-types',
            'rewrite' => false,
        ]);
    }

    /**
     * Get job data for API
     */
    public function getJobData(int $jobId): ?array
    {
        $job = get_post($jobId);
        
        if (!$job || $job->post_type !== 'job') {
            return null;
        }

        return [
            'id' => $job->ID,
            'title' => $job->post_title,
            'slug' => $job->post_name,
            'content' => $job->post_content,
            'excerpt' => $job->post_excerpt ?: wp_trim_words($job->post_content, 30),
            'status' => $job->post_status,
            'date' => $job->post_date,
            'modified' => $job->post_modified,
            'company' => get_post_meta($job->ID, 'job_company', true),
            'location' => get_post_meta($job->ID, 'job_location', true),
            'salary' => get_post_meta($job->ID, 'job_salary', true),
            'type' => get_post_meta($job->ID, 'job_type', true),
            'skills' => get_post_meta($job->ID, 'job_skills', true) ?: [],
            'apply_url' => get_post_meta($job->ID, 'job_apply_url', true),
            'featured' => (bool) get_post_meta($job->ID, 'job_featured', true),
            'categories' => wp_get_post_terms($job->ID, 'job_category', ['fields' => 'names']),
            'locations' => wp_get_post_terms($job->ID, 'job_location', ['fields' => 'names']),
            'job_types' => wp_get_post_terms($job->ID, 'job_type', ['fields' => 'names']),
        ];
    }
}
```

## Testing Checklist

### Plugin Installation
- [ ] Plugin installs without errors
- [ ] Plugin activates successfully
- [ ] Database tables are created
- [ ] Default options are set
- [ ] No PHP errors in logs

### Post Types & Taxonomies
- [ ] Job post type is registered
- [ ] Company post type is registered
- [ ] Job taxonomies are registered
- [ ] Admin menus appear correctly
- [ ] REST API endpoints are available

### Core Functionality
- [ ] Container resolves services
- [ ] JobService works correctly
- [ ] Post save triggers work
- [ ] Static regeneration is triggered
- [ ] Settings are saved properly

## Success Criteria
1. ✅ Plugin foundation is complete
2. ✅ Custom post types are working
3. ✅ Taxonomies are registered
4. ✅ Admin interface is functional
5. ✅ REST API is operational
6. ✅ Static regeneration triggers work
7. ✅ All files under 150 lines
8. ✅ Live testing passes

## Next Stage
Once Stage 13 is complete and all tests pass, proceed to **Stage 14: Job Post Type & Fields** where we'll implement the complete job post type with custom fields, meta boxes, and validation.
