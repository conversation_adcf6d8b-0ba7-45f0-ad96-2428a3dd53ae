# Static API Endpoints from WordPress

## API Generation Strategy

### WordPress to Static API Pipeline
```
WordPress (manager/) → REST API → Build Process → Static JSON Files (root/api/)
        ↓                ↓            ↓                    ↓
   Content Changes → WP-JSON → Node.js Script → Optimized API Files
```

### Generated API Structure
```
api/
├── locations/
│   ├── index.json                    (All locations with job counts)
│   ├── london/
│   │   ├── jobs.json                 (All London jobs - paginated)
│   │   ├── developer.j<PERSON>            (Developer jobs in London)
│   │   ├── site-manager.json         (Site manager jobs in London)
│   │   ├── product-manager.json      (PM jobs in London)
│   │   └── pagination/
│   │       ├── developer-page-2.json (Page 2 of developer jobs)
│   │       └── site-manager-page-2.json
│   └── manchester/
│       ├── jobs.json
│       └── developer.json
├── companies/
│   ├── index.json                    (All companies)
│   ├── google.json                   (Google company data + jobs)
│   └── microsoft.json
├── categories/
│   ├── index.json                    (All categories)
│   ├── software-engineering.json     (Category jobs)
│   └── product-management.json
├── search/
│   ├── suggestions.json              (Search autocomplete data)
│   ├── filters.json                  (Available filters)
│   └── index.json                    (Search index for client-side search)
└── meta/
    ├── site-config.json              (Site configuration)
    ├── adsense-config.json           (AdSense configuration)
    └── build-info.json               (Build timestamp, version)
```

## WordPress API Endpoints

### Custom REST API Endpoints
```php
// manager/wp-content/themes/universal-theme/includes/api-endpoints.php
class UniversalAPIEndpoints 
{
    public function __construct() 
    {
        add_action('rest_api_init', [$this, 'registerEndpoints']);
    }
    
    public function registerEndpoints(): void 
    {
        // Jobs by location and title
        register_rest_route('universal/v1', '/jobs/(?P<location>[a-zA-Z0-9-]+)/(?P<title>[a-zA-Z0-9-]+)', [
            'methods' => 'GET',
            'callback' => [$this, 'getJobsByLocationAndTitle'],
            'permission_callback' => '__return_true',
            'args' => [
                'page' => ['default' => 1, 'sanitize_callback' => 'absint'],
                'per_page' => ['default' => 20, 'sanitize_callback' => 'absint']
            ]
        ]);
        
        // All locations with job counts
        register_rest_route('universal/v1', '/locations', [
            'methods' => 'GET',
            'callback' => [$this, 'getAllLocations'],
            'permission_callback' => '__return_true'
        ]);
        
        // Jobs by company
        register_rest_route('universal/v1', '/companies/(?P<company>[a-zA-Z0-9-]+)/jobs', [
            'methods' => 'GET',
            'callback' => [$this, 'getJobsByCompany'],
            'permission_callback' => '__return_true'
        ]);
        
        // Search suggestions
        register_rest_route('universal/v1', '/search/suggestions', [
            'methods' => 'GET',
            'callback' => [$this, 'getSearchSuggestions'],
            'permission_callback' => '__return_true',
            'args' => [
                'q' => ['required' => true, 'sanitize_callback' => 'sanitize_text_field']
            ]
        ]);
        
        // Trigger static API regeneration
        register_rest_route('universal/v1', '/regenerate-api', [
            'methods' => 'POST',
            'callback' => [$this, 'triggerAPIRegeneration'],
            'permission_callback' => [$this, 'checkRegenerationPermission']
        ]);
        
        // AdSense configuration
        register_rest_route('universal/v1', '/adsense-config', [
            'methods' => 'GET',
            'callback' => [$this, 'getAdSenseConfig'],
            'permission_callback' => '__return_true'
        ]);
    }
    
    public function getJobsByLocationAndTitle(WP_REST_Request $request): WP_REST_Response 
    {
        $location = $request->get_param('location');
        $jobTitle = $request->get_param('title');
        $page = $request->get_param('page');
        $perPage = $request->get_param('per_page');
        
        $args = [
            'post_type' => 'job',
            'post_status' => 'publish',
            'posts_per_page' => $perPage,
            'paged' => $page,
            'meta_query' => [
                'relation' => 'AND',
                [
                    'key' => 'job_location_slug',
                    'value' => $location,
                    'compare' => '='
                ]
            ]
        ];
        
        // Add job title search
        if ($jobTitle !== 'all') {
            $args['s'] = str_replace('-', ' ', $jobTitle);
        }
        
        $query = new WP_Query($args);
        $jobs = [];
        
        foreach ($query->posts as $post) {
            $jobs[] = $this->formatJobData($post);
        }
        
        return new WP_REST_Response([
            'jobs' => $jobs,
            'total' => $query->found_posts,
            'pages' => $query->max_num_pages,
            'current_page' => $page,
            'per_page' => $perPage,
            'location' => $location,
            'job_title' => $jobTitle,
            'seo' => $this->generateSEOData($location, $jobTitle, $query->found_posts)
        ]);
    }
    
    public function getAllLocations(): WP_REST_Response 
    {
        global $wpdb;
        
        $locations = $wpdb->get_results("
            SELECT 
                pm.meta_value as location_slug,
                pm2.meta_value as location_name,
                COUNT(p.ID) as job_count
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = 'job_location_slug'
            INNER JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = 'job_location_name'
            WHERE p.post_type = 'job' AND p.post_status = 'publish'
            GROUP BY pm.meta_value, pm2.meta_value
            ORDER BY job_count DESC
        ");
        
        $formattedLocations = array_map(function($location) {
            return [
                'slug' => $location->location_slug,
                'name' => $location->location_name,
                'job_count' => (int) $location->job_count,
                'url' => "/locations/{$location->location_slug}/",
                'popular_titles' => $this->getPopularJobTitles($location->location_slug)
            ];
        }, $locations);
        
        return new WP_REST_Response([
            'locations' => $formattedLocations,
            'total' => count($formattedLocations)
        ]);
    }
    
    public function getAdSenseConfig(): WP_REST_Response 
    {
        $config = [
            'client_id' => get_option('adsense_client_id', ''),
            'slots' => [
                'header' => get_option('adsense_slot_header', ''),
                'content' => get_option('adsense_slot_content', ''),
                'sidebar' => get_option('adsense_slot_sidebar', ''),
                'footer' => get_option('adsense_slot_footer', ''),
                'inline' => get_option('adsense_slot_inline', '')
            ],
            'auto_ads' => get_option('adsense_auto_ads', false),
            'lazy_load' => get_option('adsense_lazy_load', true),
            'responsive' => get_option('adsense_responsive', true)
        ];
        
        return new WP_REST_Response($config);
    }
    
    private function formatJobData(WP_Post $post): array 
    {
        return [
            'id' => $post->ID,
            'title' => $post->post_title,
            'slug' => $post->post_name,
            'excerpt' => wp_trim_words($post->post_content, 30),
            'content' => $post->post_content,
            'company' => get_post_meta($post->ID, 'job_company', true),
            'company_slug' => get_post_meta($post->ID, 'job_company_slug', true),
            'location' => get_post_meta($post->ID, 'job_location_name', true),
            'location_slug' => get_post_meta($post->ID, 'job_location_slug', true),
            'salary' => get_post_meta($post->ID, 'job_salary', true),
            'type' => get_post_meta($post->ID, 'job_type', true),
            'skills' => get_post_meta($post->ID, 'job_skills', true) ?: [],
            'posted_date' => $post->post_date,
            'modified_date' => $post->post_modified,
            'featured' => get_post_meta($post->ID, 'job_featured', true),
            'url' => "/jobs/{$post->post_name}.html",
            'apply_url' => get_post_meta($post->ID, 'job_apply_url', true),
            'schema' => $this->generateJobSchema($post)
        ];
    }
    
    private function generateSEOData(string $location, string $jobTitle, int $jobCount): array 
    {
        $locationName = ucwords(str_replace('-', ' ', $location));
        $titleName = ucwords(str_replace('-', ' ', $jobTitle));
        
        return [
            'title' => "{$titleName} Jobs in {$locationName} - {$jobCount} Positions Available",
            'description' => "Find {$jobCount} {$titleName} jobs in {$locationName}. Apply to top companies. Competitive salaries and benefits.",
            'keywords' => [
                $titleName,
                $locationName,
                "{$titleName} jobs",
                "{$titleName} {$locationName}",
                "jobs in {$locationName}"
            ],
            'canonical' => "/locations/{$location}/{$jobTitle}/",
            'og_title' => "{$titleName} Jobs in {$locationName}",
            'og_description' => "Discover {$jobCount} {$titleName} opportunities in {$locationName}. Start your career today!",
            'twitter_title' => "{$titleName} Jobs in {$locationName}",
            'twitter_description' => "{$jobCount} {$titleName} positions available in {$locationName}. Apply now!"
        ];
    }
}

// Initialize API endpoints
new UniversalAPIEndpoints();
```

## Static API Generation Script

### Node.js API Builder
```javascript
// scripts/build-static-api.js
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

class StaticAPIBuilder {
    constructor() {
        this.wpApiBase = process.env.WP_API_URL || 'http://localhost/manager/wp-json/universal/v1';
        this.outputDir = 'api';
        this.maxRetries = 3;
        this.retryDelay = 1000;
    }
    
    async buildAllAPIs() {
        console.log('🔄 Building static API endpoints...');
        const startTime = Date.now();
        
        try {
            // Create output directory
            await fs.mkdir(this.outputDir, { recursive: true });
            
            // Build location APIs
            await this.buildLocationAPIs();
            
            // Build company APIs
            await this.buildCompanyAPIs();
            
            // Build category APIs
            await this.buildCategoryAPIs();
            
            // Build search APIs
            await this.buildSearchAPIs();
            
            // Build meta APIs
            await this.buildMetaAPIs();
            
            const duration = Date.now() - startTime;
            console.log(`✅ Static APIs built successfully in ${(duration / 1000).toFixed(2)}s`);
            
        } catch (error) {
            console.error('❌ API build failed:', error);
            process.exit(1);
        }
    }
    
    async buildLocationAPIs() {
        console.log('📍 Building location APIs...');
        
        // Get all locations
        const locations = await this.fetchWithRetry('/locations');
        
        // Save locations index
        await this.saveJSON('locations/index.json', locations);
        
        // Build individual location APIs
        for (const location of locations.data.locations) {
            await this.buildLocationAPI(location);
        }
    }
    
    async buildLocationAPI(location) {
        const locationSlug = location.slug;
        const locationDir = path.join(this.outputDir, 'locations', locationSlug);
        
        // Create location directory
        await fs.mkdir(locationDir, { recursive: true });
        
        // Get all jobs for this location
        const allJobs = await this.fetchWithRetry(`/jobs/${locationSlug}/all`);
        await this.saveJSON(`locations/${locationSlug}/jobs.json`, allJobs);
        
        // Build job title specific APIs
        for (const jobTitle of location.popular_titles) {
            await this.buildJobTitleAPI(locationSlug, jobTitle);
        }
    }
    
    async buildJobTitleAPI(location, jobTitle) {
        try {
            const response = await this.fetchWithRetry(`/jobs/${location}/${jobTitle}`);
            
            if (response.data.total > 0) {
                await this.saveJSON(`locations/${location}/${jobTitle}.json`, response);
                
                // Build pagination if needed
                if (response.data.pages > 1) {
                    await this.buildJobTitlePagination(location, jobTitle, response.data.pages);
                }
            }
            
        } catch (error) {
            console.warn(`⚠️  Skipping ${location}/${jobTitle}: ${error.message}`);
        }
    }
    
    async buildJobTitlePagination(location, jobTitle, totalPages) {
        const paginationDir = path.join(this.outputDir, 'locations', location, 'pagination');
        await fs.mkdir(paginationDir, { recursive: true });
        
        for (let page = 2; page <= Math.min(totalPages, 10); page++) {
            try {
                const response = await this.fetchWithRetry(`/jobs/${location}/${jobTitle}?page=${page}`);
                await this.saveJSON(`locations/${location}/pagination/${jobTitle}-page-${page}.json`, response);
            } catch (error) {
                console.warn(`⚠️  Failed to build page ${page} for ${location}/${jobTitle}`);
            }
        }
    }
    
    async buildSearchAPIs() {
        console.log('🔍 Building search APIs...');
        
        const searchDir = path.join(this.outputDir, 'search');
        await fs.mkdir(searchDir, { recursive: true });
        
        // Build search suggestions
        const suggestions = await this.buildSearchSuggestions();
        await this.saveJSON('search/suggestions.json', suggestions);
        
        // Build search filters
        const filters = await this.buildSearchFilters();
        await this.saveJSON('search/filters.json', filters);
        
        // Build search index for client-side search
        const searchIndex = await this.buildSearchIndex();
        await this.saveJSON('search/index.json', searchIndex);
    }
    
    async buildMetaAPIs() {
        console.log('⚙️  Building meta APIs...');
        
        const metaDir = path.join(this.outputDir, 'meta');
        await fs.mkdir(metaDir, { recursive: true });
        
        // Site configuration
        const siteConfig = await this.fetchWithRetry('/site-config');
        await this.saveJSON('meta/site-config.json', siteConfig);
        
        // AdSense configuration
        const adsenseConfig = await this.fetchWithRetry('/adsense-config');
        await this.saveJSON('meta/adsense-config.json', adsenseConfig);
        
        // Build info
        const buildInfo = {
            timestamp: new Date().toISOString(),
            version: process.env.npm_package_version || '1.0.0',
            environment: process.env.NODE_ENV || 'production',
            api_endpoints: await this.generateAPIManifest()
        };
        await this.saveJSON('meta/build-info.json', buildInfo);
    }
    
    async fetchWithRetry(endpoint, retries = this.maxRetries) {
        for (let i = 0; i < retries; i++) {
            try {
                const response = await axios.get(`${this.wpApiBase}${endpoint}`, {
                    timeout: 30000,
                    headers: {
                        'User-Agent': 'StaticAPIBuilder/1.0'
                    }
                });
                return response.data;
            } catch (error) {
                if (i === retries - 1) throw error;
                
                console.warn(`⚠️  Retry ${i + 1}/${retries} for ${endpoint}: ${error.message}`);
                await this.delay(this.retryDelay * (i + 1));
            }
        }
    }
    
    async saveJSON(filePath, data) {
        const fullPath = path.join(this.outputDir, filePath);
        const dir = path.dirname(fullPath);
        
        await fs.mkdir(dir, { recursive: true });
        await fs.writeFile(fullPath, JSON.stringify(data, null, 2));
        
        console.log(`✅ Saved ${filePath}`);
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    async generateAPIManifest() {
        // Generate a manifest of all available API endpoints
        const manifest = {
            locations: [],
            companies: [],
            categories: [],
            search: ['suggestions', 'filters', 'index'],
            meta: ['site-config', 'adsense-config', 'build-info']
        };
        
        // Scan generated files to build manifest
        try {
            const locationsDir = path.join(this.outputDir, 'locations');
            const locations = await fs.readdir(locationsDir);
            
            for (const location of locations) {
                if (location !== 'index.json') {
                    const locationFiles = await fs.readdir(path.join(locationsDir, location));
                    manifest.locations.push({
                        slug: location,
                        endpoints: locationFiles.filter(f => f.endsWith('.json')).map(f => f.replace('.json', ''))
                    });
                }
            }
        } catch (error) {
            console.warn('Could not generate locations manifest:', error.message);
        }
        
        return manifest;
    }
}

// Run the builder
if (require.main === module) {
    new StaticAPIBuilder().buildAllAPIs();
}

module.exports = StaticAPIBuilder;
```

## Automatic Regeneration System

### WordPress Hooks for API Updates
```php
// manager/wp-content/themes/universal-theme/includes/api-regeneration.php
class APIRegenerationHandler 
{
    public function __construct() 
    {
        add_action('save_post', [$this, 'handlePostSave'], 10, 2);
        add_action('delete_post', [$this, 'handlePostDelete']);
        add_action('updated_option', [$this, 'handleOptionUpdate'], 10, 3);
    }
    
    public function handlePostSave(int $postId, WP_Post $post): void 
    {
        if ($post->post_type !== 'job' || $post->post_status !== 'publish') {
            return;
        }
        
        $location = get_post_meta($postId, 'job_location_slug', true);
        $jobTitle = $this->extractJobTitleSlug($post->post_title);
        
        // Queue API regeneration
        $this->queueAPIRegeneration([
            'type' => 'job_updated',
            'location' => $location,
            'job_title' => $jobTitle,
            'job_id' => $postId
        ]);
    }
    
    public function handleOptionUpdate(string $option, mixed $oldValue, mixed $newValue): void 
    {
        if (str_starts_with($option, 'adsense_')) {
            $this->queueAPIRegeneration([
                'type' => 'adsense_config_updated',
                'option' => $option
            ]);
        }
    }
    
    private function queueAPIRegeneration(array $data): void 
    {
        // Use WordPress cron for background processing
        wp_schedule_single_event(time() + 60, 'regenerate_static_api', [$data]);
    }
}

// Register cron handler
add_action('regenerate_static_api', function($data) {
    $command = "cd " . ABSPATH . "../ && npm run build:api:incremental " . escapeshellarg(json_encode($data));
    exec($command . " > /dev/null 2>&1 &");
});

new APIRegenerationHandler();
```
