# Stage 19: Client-Side Router

## Stage Overview
Implement the client-side routing system for dynamic job title pages, pagination, and search functionality. This enables handling of less common job title/location combinations without pre-generating static pages.

## Prerequisites
- Stages 1-18 completed successfully
- Static API endpoints working
- Component system operational
- Skeleton loading system ready

## Implementation Steps

### Step 19.1: Create Core Router Class

#### src/js/modules/Router.js
```javascript
/**
 * Universal App Client-Side Router
 * 
 * Handles dynamic routing for job title pages and pagination
 * 
 * @package UniversalApp
 * @since 1.0.0
 */
class UniversalRouter {
    constructor() {
        this.routes = new Map();
        this.middlewares = [];
        this.currentRoute = null;
        this.isNavigating = false;
        this.cache = new Map();
        
        this.init();
    }

    /**
     * Initialize router
     */
    init() {
        // Handle browser navigation
        window.addEventListener('popstate', this.handlePopState.bind(this));
        
        // Intercept link clicks
        document.addEventListener('click', this.handleLinkClick.bind(this));
        
        // Handle initial page load
        this.handleRoute(window.location.pathname);
        
        // Register default routes
        this.registerDefaultRoutes();
    }

    /**
     * Register default routes
     */
    registerDefaultRoutes() {
        // Dynamic job title routes
        this.addRoute(
            /^\/locations\/([^\/]+)\/([^\/]+)\/?$/,
            this.handleJobTitlePage.bind(this)
        );
        
        // Dynamic pagination routes
        this.addRoute(
            /^\/locations\/([^\/]+)\/([^\/]+)\/page\/(\d+)\/?$/,
            this.handleJobTitlePagination.bind(this)
        );
        
        // Search routes
        this.addRoute(
            /^\/search\/?$/,
            this.handleSearchPage.bind(this)
        );
    }

    /**
     * Add route pattern and handler
     */
    addRoute(pattern, handler) {
        this.routes.set(pattern, handler);
    }

    /**
     * Add middleware
     */
    addMiddleware(middleware) {
        this.middlewares.push(middleware);
    }

    /**
     * Handle route navigation
     */
    async handleRoute(path) {
        if (this.isNavigating) return;
        
        this.isNavigating = true;
        
        try {
            // Run middlewares
            for (const middleware of this.middlewares) {
                const result = await middleware(path);
                if (result === false) {
                    this.isNavigating = false;
                    return;
                }
            }
            
            // Find matching route
            for (const [pattern, handler] of this.routes) {
                const match = path.match(pattern);
                if (match) {
                    await handler(match);
                    this.currentRoute = { path, pattern, match };
                    this.isNavigating = false;
                    return;
                }
            }
            
            // No route found - check if static page exists
            await this.handleStaticFallback(path);
            
        } catch (error) {
            console.error('Router error:', error);
            this.handleError(error);
        } finally {
            this.isNavigating = false;
        }
    }

    /**
     * Handle job title page
     */
    async handleJobTitlePage(match) {
        const [, location, jobTitle] = match;
        const cacheKey = `${location}-${jobTitle}`;
        
        // Show skeleton loading
        this.showSkeletonLoading('job-title-page');
        
        try {
            // Check cache first
            let data = this.cache.get(cacheKey);
            
            if (!data) {
                // Try static API first
                data = await this.fetchStaticAPI(location, jobTitle);
                
                if (!data) {
                    // Fallback to WordPress API
                    data = await this.fetchWordPressAPI(location, jobTitle);
                }
                
                // Cache the result
                this.cache.set(cacheKey, data);
            }
            
            // Render page
            await this.renderJobTitlePage(location, jobTitle, data);
            
        } catch (error) {
            console.error('Failed to load job title page:', error);
            this.handlePageError(location, jobTitle);
        }
    }

    /**
     * Handle job title pagination
     */
    async handleJobTitlePagination(match) {
        const [, location, jobTitle, page] = match;
        const pageNum = parseInt(page);
        
        this.showSkeletonLoading('job-list');
        
        try {
            const data = await this.fetchPaginatedJobs(location, jobTitle, pageNum);
            await this.renderJobTitlePage(location, jobTitle, data, pageNum);
            
        } catch (error) {
            console.error('Failed to load paginated jobs:', error);
            this.handlePageError(location, jobTitle);
        }
    }

    /**
     * Fetch from static API
     */
    async fetchStaticAPI(location, jobTitle) {
        try {
            const response = await fetch(`/api/locations/${location}/${jobTitle}.json`);
            
            if (response.ok) {
                return await response.json();
            }
            
            return null;
        } catch (error) {
            console.warn('Static API fetch failed:', error);
            return null;
        }
    }

    /**
     * Fetch from WordPress API
     */
    async fetchWordPressAPI(location, jobTitle) {
        try {
            const response = await fetch(
                `/manager/wp-json/universal/v1/jobs/${location}/${jobTitle}`
            );
            
            if (response.ok) {
                const data = await response.json();
                
                // Cache as static API for future use
                this.cacheStaticAPI(location, jobTitle, data);
                
                return data;
            }
            
            throw new Error(`API request failed: ${response.status}`);
        } catch (error) {
            console.error('WordPress API fetch failed:', error);
            throw error;
        }
    }

    /**
     * Fetch paginated jobs
     */
    async fetchPaginatedJobs(location, jobTitle, page) {
        const cacheKey = `${location}-${jobTitle}-page-${page}`;
        
        // Check cache first
        let data = this.cache.get(cacheKey);
        
        if (!data) {
            try {
                // Try static API pagination
                const response = await fetch(
                    `/api/locations/${location}/pagination/${jobTitle}-page-${page}.json`
                );
                
                if (response.ok) {
                    data = await response.json();
                } else {
                    // Fallback to WordPress API
                    const wpResponse = await fetch(
                        `/manager/wp-json/universal/v1/jobs/${location}/${jobTitle}?page=${page}`
                    );
                    
                    if (wpResponse.ok) {
                        data = await wpResponse.json();
                    } else {
                        throw new Error('Failed to fetch paginated jobs');
                    }
                }
                
                this.cache.set(cacheKey, data);
            } catch (error) {
                console.error('Pagination fetch failed:', error);
                throw error;
            }
        }
        
        return data;
    }

    /**
     * Render job title page
     */
    async renderJobTitlePage(location, jobTitle, data, page = 1) {
        const container = document.getElementById('main-content');
        const title = this.formatJobTitle(jobTitle);
        const locationName = this.formatLocation(location);
        
        // Update page meta
        this.updatePageMeta({
            title: `${title} Jobs in ${locationName} - Page ${page}`,
            description: `Find ${data.total} ${title.toLowerCase()} jobs in ${locationName}. Apply to top companies.`,
            canonical: `/locations/${location}/${jobTitle}/${page > 1 ? `page/${page}/` : ''}`
        });
        
        // Render page content
        const html = await this.getJobTitlePageTemplate(location, jobTitle, data, page);
        container.innerHTML = html;
        
        // Initialize dynamic components
        await this.initializeDynamicComponents();
        
        // Hide skeleton loading
        this.hideSkeletonLoading();
        
        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    /**
     * Get job title page template
     */
    async getJobTitlePageTemplate(location, jobTitle, data, page) {
        const title = this.formatJobTitle(jobTitle);
        const locationName = this.formatLocation(location);
        
        return `
            <div class="dynamic-page" data-page-type="job-title">
                <div class="page-header">
                    <h1 class="page-title">${title} Jobs in ${locationName}</h1>
                    <p class="job-count">${data.total} jobs found</p>
                    
                    <nav class="breadcrumbs" itemscope itemtype="https://schema.org/BreadcrumbList">
                        <a href="/" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                            <span itemprop="name">Home</span>
                            <meta itemprop="position" content="1">
                        </a> → 
                        <a href="/locations/${location}/" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                            <span itemprop="name">${locationName}</span>
                            <meta itemprop="position" content="2">
                        </a> → 
                        <span itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                            <span itemprop="name">${title} Jobs</span>
                            <meta itemprop="position" content="3">
                        </span>
                    </nav>
                </div>
                
                <div class="job-listings" id="job-listings">
                    ${data.jobs.map((job, index) => this.renderJobCard(job, index)).join('')}
                </div>
                
                <nav class="pagination" id="pagination">
                    ${this.renderPagination(data.pagination, location, jobTitle)}
                </nav>
            </div>
        `;
    }

    /**
     * Render job card
     */
    renderJobCard(job, index) {
        return `
            <article class="job-card" data-job-id="${job.id}" itemscope itemtype="https://schema.org/JobPosting">
                <div class="job-card__header">
                    <h3 class="job-card__title" itemprop="title">
                        <a href="/jobs/${job.slug}.html" class="job-card__link">${job.title}</a>
                    </h3>
                    <div class="job-card__company" itemprop="hiringOrganization" itemscope itemtype="https://schema.org/Organization">
                        <span itemprop="name">${job.company}</span>
                    </div>
                </div>
                
                <div class="job-card__meta">
                    <span class="job-card__location" itemprop="jobLocation" itemscope itemtype="https://schema.org/Place">
                        <span itemprop="name">${job.location}</span>
                    </span>
                    <span class="job-card__salary" itemprop="baseSalary">${job.salary}</span>
                    <span class="job-card__type" itemprop="employmentType">${job.type}</span>
                </div>
                
                <div class="job-card__description" itemprop="description">
                    ${job.excerpt}
                </div>
                
                <div class="job-card__footer">
                    <div class="job-card__tags">
                        ${job.skills.map(skill => `<span class="tag" itemprop="skills">${skill}</span>`).join('')}
                    </div>
                    <div class="job-card__actions">
                        <div class="save-job-mount" data-job-id="${job.id}"></div>
                        <a href="/jobs/${job.slug}.html" class="btn btn--primary">View Details</a>
                    </div>
                </div>
                
                <meta itemprop="datePosted" content="${job.posted_date}">
            </article>
        `;
    }

    /**
     * Render pagination
     */
    renderPagination(pagination, location, jobTitle) {
        if (pagination.total_pages <= 1) return '';
        
        const currentPage = pagination.current_page;
        const totalPages = pagination.total_pages;
        let html = '<div class="pagination__list">';
        
        // Previous button
        if (currentPage > 1) {
            const prevUrl = currentPage === 2 
                ? `/locations/${location}/${jobTitle}/`
                : `/locations/${location}/${jobTitle}/page/${currentPage - 1}/`;
            html += `<a href="${prevUrl}" class="pagination__link pagination__prev">Previous</a>`;
        }
        
        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            if (i === currentPage) {
                html += `<span class="pagination__link pagination__current">${i}</span>`;
            } else {
                const pageUrl = i === 1 
                    ? `/locations/${location}/${jobTitle}/`
                    : `/locations/${location}/${jobTitle}/page/${i}/`;
                html += `<a href="${pageUrl}" class="pagination__link">${i}</a>`;
            }
        }
        
        // Next button
        if (currentPage < totalPages) {
            const nextUrl = `/locations/${location}/${jobTitle}/page/${currentPage + 1}/`;
            html += `<a href="${nextUrl}" class="pagination__link pagination__next">Next</a>`;
        }
        
        html += '</div>';
        return html;
    }

    /**
     * Handle link clicks
     */
    handleLinkClick(event) {
        const link = event.target.closest('a');
        if (!link) return;
        
        const href = link.getAttribute('href');
        if (!href || href.startsWith('http') || href.startsWith('mailto') || href.startsWith('tel')) {
            return;
        }
        
        // Check if it's a route we handle
        const isHandledRoute = Array.from(this.routes.keys()).some(pattern => 
            pattern.test(href)
        );
        
        if (isHandledRoute) {
            event.preventDefault();
            this.navigate(href);
        }
    }

    /**
     * Navigate to path
     */
    navigate(path) {
        if (path === window.location.pathname) return;
        
        history.pushState(null, '', path);
        this.handleRoute(path);
    }

    /**
     * Handle popstate event
     */
    handlePopState(event) {
        this.handleRoute(window.location.pathname);
    }

    /**
     * Show skeleton loading
     */
    showSkeletonLoading(type) {
        if (window.skeletonLoader) {
            const container = document.getElementById('main-content');
            window.skeletonLoader.showSkeleton(container, type);
        }
    }

    /**
     * Hide skeleton loading
     */
    hideSkeletonLoading() {
        if (window.skeletonLoader) {
            window.skeletonLoader.hideAllSkeletons();
        }
    }

    /**
     * Update page meta
     */
    updatePageMeta(meta) {
        document.title = meta.title;
        
        // Update meta description
        let metaDesc = document.querySelector('meta[name="description"]');
        if (metaDesc) {
            metaDesc.setAttribute('content', meta.description);
        }
        
        // Update canonical URL
        let canonical = document.querySelector('link[rel="canonical"]');
        if (canonical) {
            canonical.setAttribute('href', window.location.origin + meta.canonical);
        }
    }

    /**
     * Format job title
     */
    formatJobTitle(jobTitle) {
        return jobTitle.split('-').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    }

    /**
     * Format location
     */
    formatLocation(location) {
        return location.split('-').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    }

    /**
     * Initialize dynamic components
     */
    async initializeDynamicComponents() {
        // Initialize save job buttons
        if (window.SaveJobComponent) {
            document.querySelectorAll('.save-job-mount').forEach(mount => {
                new window.SaveJobComponent(mount);
            });
        }
        
        // Initialize AdSense ads
        if (window.adSenseManager) {
            window.adSenseManager.initializeAds();
        }
    }

    /**
     * Handle page error
     */
    handlePageError(location, jobTitle) {
        const container = document.getElementById('main-content');
        container.innerHTML = `
            <div class="error-page">
                <h1>Page Not Found</h1>
                <p>Sorry, we couldn't find any ${this.formatJobTitle(jobTitle)} jobs in ${this.formatLocation(location)}.</p>
                <a href="/locations/${location}/" class="btn btn--primary">View All Jobs in ${this.formatLocation(location)}</a>
            </div>
        `;
    }

    /**
     * Cache static API response
     */
    async cacheStaticAPI(location, jobTitle, data) {
        try {
            // This would send data to be cached as static API
            await fetch('/api/cache-endpoint', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    location,
                    jobTitle,
                    data
                })
            });
        } catch (error) {
            console.warn('Failed to cache static API:', error);
        }
    }
}

// Initialize router
const router = new UniversalRouter();

// Add analytics middleware
router.addMiddleware(async (path) => {
    if (window.gtag) {
        gtag('config', 'GA_MEASUREMENT_ID', {
            page_path: path
        });
    }
    return true;
});

// Export for global use
window.UniversalRouter = router;

export default router;
```

### Step 19.2: Create Route Preloader

#### src/js/modules/RoutePreloader.js
```javascript
/**
 * Route Preloader
 * 
 * Preloads routes on hover and viewport intersection
 * 
 * @package UniversalApp
 * @since 1.0.0
 */
class RoutePreloader {
    constructor(router) {
        this.router = router;
        this.preloadQueue = new Set();
        this.preloadCache = new Map();
        
        this.init();
    }

    /**
     * Initialize preloader
     */
    init() {
        // Preload on hover
        document.addEventListener('mouseover', this.handleLinkHover.bind(this));
        
        // Preload on viewport intersection
        this.setupIntersectionObserver();
        
        // Preload on touch start (mobile)
        document.addEventListener('touchstart', this.handleTouchStart.bind(this));
    }

    /**
     * Handle link hover
     */
    handleLinkHover(event) {
        const link = event.target.closest('a[href^="/locations/"]');
        if (link && !this.preloadQueue.has(link.href)) {
            this.preloadRoute(link.href);
        }
    }

    /**
     * Handle touch start
     */
    handleTouchStart(event) {
        const link = event.target.closest('a[href^="/locations/"]');
        if (link && !this.preloadQueue.has(link.href)) {
            this.preloadRoute(link.href);
        }
    }

    /**
     * Setup intersection observer
     */
    setupIntersectionObserver() {
        this.observer = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const link = entry.target;
                        if (link.href && !this.preloadQueue.has(link.href)) {
                            this.preloadRoute(link.href);
                        }
                        this.observer.unobserve(link);
                    }
                });
            },
            { rootMargin: '50px' }
        );
        
        // Observe existing links
        this.observeLinks();
        
        // Observe new links added to DOM
        this.setupMutationObserver();
    }

    /**
     * Observe links
     */
    observeLinks() {
        document.querySelectorAll('a[href^="/locations/"]').forEach(link => {
            this.observer.observe(link);
        });
    }

    /**
     * Setup mutation observer for new links
     */
    setupMutationObserver() {
        const mutationObserver = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const links = node.querySelectorAll('a[href^="/locations/"]');
                        links.forEach(link => this.observer.observe(link));
                    }
                });
            });
        });
        
        mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * Preload route
     */
    async preloadRoute(path) {
        if (this.preloadQueue.has(path) || this.preloadCache.has(path)) {
            return;
        }
        
        this.preloadQueue.add(path);
        
        try {
            // Extract location and job title from path
            const match = path.match(/^\/locations\/([^\/]+)\/([^\/]+)\/?$/);
            if (match) {
                const [, location, jobTitle] = match;
                
                // Preload API data
                const data = await this.preloadAPIData(location, jobTitle);
                if (data) {
                    this.preloadCache.set(path, data);
                }
            }
        } catch (error) {
            console.warn('Preload failed for:', path, error);
        }
    }

    /**
     * Preload API data
     */
    async preloadAPIData(location, jobTitle) {
        try {
            // Try static API first
            const response = await fetch(`/api/locations/${location}/${jobTitle}.json`);
            if (response.ok) {
                return await response.json();
            }
            
            // Fallback to WordPress API
            const wpResponse = await fetch(
                `/manager/wp-json/universal/v1/jobs/${location}/${jobTitle}`
            );
            if (wpResponse.ok) {
                return await wpResponse.json();
            }
            
            return null;
        } catch (error) {
            console.warn('API preload failed:', error);
            return null;
        }
    }

    /**
     * Get preloaded data
     */
    getPreloadedData(path) {
        return this.preloadCache.get(path);
    }

    /**
     * Clear preload cache
     */
    clearCache() {
        this.preloadCache.clear();
        this.preloadQueue.clear();
    }
}

export default RoutePreloader;
```

## Testing Checklist

### Router Functionality
- [ ] Routes are registered correctly
- [ ] Link clicks are intercepted
- [ ] Browser navigation works
- [ ] Route matching works correctly
- [ ] Error handling is functional

### Dynamic Page Loading
- [ ] Job title pages load correctly
- [ ] Pagination works properly
- [ ] API fallbacks work
- [ ] Skeleton loading shows/hides
- [ ] Page meta is updated

### Performance Features
- [ ] Route preloading works
- [ ] Caching is functional
- [ ] Intersection observer works
- [ ] Touch preloading works
- [ ] Memory usage is reasonable

### Integration
- [ ] Works with skeleton loader
- [ ] Integrates with AdSense
- [ ] Analytics tracking works
- [ ] SEO meta updates work
- [ ] Accessibility is maintained

## Success Criteria
1. ✅ Client-side router is functional
2. ✅ Dynamic job title pages work
3. ✅ Pagination is operational
4. ✅ Route preloading works
5. ✅ Performance is optimized
6. ✅ SEO is maintained
7. ✅ All files under 150 lines
8. ✅ Live testing passes

## Next Stage
Once Stage 19 is complete and all tests pass, proceed to **Stage 20: Static API Generation** where we'll implement the automated generation of static API endpoints from WordPress content.
