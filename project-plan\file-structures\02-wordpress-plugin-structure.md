# WordPress Universal Plugin File Structure

## Plugin Architecture Overview

### Advanced PHP 8+ Implementation
- **Namespaces**: `UniversalApp\Plugin\Jobs\*`
- **Attributes**: Metadata and configuration
- **Enums**: Type-safe constants
- **Readonly Properties**: Immutable data
- **Union Types**: Flexible type definitions
- **Match Expressions**: Modern control flow
- **Fibers**: Asynchronous processing

## Root Plugin Structure
```
universal-jobs-plugin/
├── universal-jobs.php                 (Main plugin file)
├── uninstall.php                      (Cleanup on uninstall)
├── composer.json                      (Dependencies & autoloading)
├── package.json                       (Frontend dependencies)
├── webpack.config.js                  (Asset bundling)
├── .env.example                       (Environment variables)
├── phpunit.xml                        (Testing configuration)
├── rector.php                         (Code modernization)
└── README.md                          (Documentation)
```

## Core Plugin Structure
```
src/
├── Core/                              (Core plugin functionality)
│   ├── Plugin.php                     (Main plugin class)
│   ├── Activator.php                  (Plugin activation)
│   ├── Deactivator.php                (Plugin deactivation)
│   ├── Container.php                  (DI container)
│   ├── ServiceProvider.php           (Service registration)
│   └── Bootstrap.php                  (Plugin initialization)
├── Admin/                             (Admin functionality)
│   ├── AdminController.php           (Admin controller)
│   ├── MenuManager.php               (Admin menu management)
│   ├── SettingsManager.php           (Settings handling)
│   ├── MetaBoxManager.php            (Meta box management)
│   └── AjaxHandler.php               (AJAX request handling)
├── Frontend/                          (Frontend functionality)
│   ├── FrontendController.php        (Frontend controller)
│   ├── ShortcodeManager.php          (Shortcode handling)
│   ├── TemplateLoader.php            (Template loading)
│   ├── AssetManager.php              (Asset management)
│   └── RestApiController.php         (REST API endpoints)
├── Models/                            (Data models)
│   ├── AbstractModel.php             (Base model class)
│   ├── Job.php                       (Job model)
│   ├── Company.php                   (Company model)
│   ├── Application.php               (Job application model)
│   ├── Category.php                  (Category model)
│   └── Location.php                  (Location model)
├── Services/                          (Business logic services)
│   ├── JobService.php                (Job management)
│   ├── CompanyService.php            (Company management)
│   ├── ApplicationService.php        (Application handling)
│   ├── SearchService.php             (Search functionality)
│   ├── EmailService.php              (Email notifications)
│   ├── CacheService.php              (Caching layer)
│   └── StaticExportService.php       (Static site export)
├── Database/                          (Database operations)
│   ├── DatabaseManager.php           (Database management)
│   ├── Migrations/                   (Database migrations)
│   │   ├── CreateJobsTable.php       (Jobs table migration)
│   │   ├── CreateCompaniesTable.php  (Companies table migration)
│   │   ├── CreateApplicationsTable.php (Applications migration)
│   │   └── CreateCategoriesTable.php (Categories migration)
│   ├── Seeders/                      (Database seeders)
│   │   ├── JobSeeder.php             (Sample job data)
│   │   ├── CompanySeeder.php         (Sample company data)
│   │   └── CategorySeeder.php        (Sample category data)
│   └── Repositories/                 (Data access layer)
│       ├── JobRepository.php         (Job data access)
│       ├── CompanyRepository.php     (Company data access)
│       ├── ApplicationRepository.php (Application data access)
│       └── CategoryRepository.php    (Category data access)
├── API/                               (REST API endpoints)
│   ├── Controllers/                  (API controllers)
│   │   ├── JobsController.php        (Jobs API)
│   │   ├── CompaniesController.php   (Companies API)
│   │   ├── ApplicationsController.php (Applications API)
│   │   ├── CategoriesController.php  (Categories API)
│   │   └── SearchController.php      (Search API)
│   ├── Middleware/                   (API middleware)
│   │   ├── AuthMiddleware.php        (Authentication)
│   │   ├── RateLimitMiddleware.php   (Rate limiting)
│   │   ├── CorsMiddleware.php        (CORS handling)
│   │   └── ValidationMiddleware.php  (Request validation)
│   ├── Validators/                   (Request validators)
│   │   ├── JobValidator.php          (Job validation)
│   │   ├── CompanyValidator.php      (Company validation)
│   │   └── ApplicationValidator.php  (Application validation)
│   └── Transformers/                 (Data transformers)
│       ├── JobTransformer.php        (Job data transformation)
│       ├── CompanyTransformer.php    (Company data transformation)
│       └── ApplicationTransformer.php (Application transformation)
├── Events/                            (Event system)
│   ├── EventDispatcher.php           (Event dispatcher)
│   ├── Listeners/                    (Event listeners)
│   │   ├── JobCreatedListener.php    (Job creation events)
│   │   ├── ApplicationSubmittedListener.php (Application events)
│   │   └── CompanyUpdatedListener.php (Company update events)
│   └── Events/                       (Event classes)
│       ├── JobCreated.php            (Job created event)
│       ├── ApplicationSubmitted.php  (Application submitted event)
│       └── CompanyUpdated.php        (Company updated event)
├── Traits/                            (Reusable functionality)
│   ├── Singleton.php                 (Singleton pattern)
│   ├── Hookable.php                  (WordPress hooks)
│   ├── Cacheable.php                 (Caching functionality)
│   ├── Validatable.php               (Validation functionality)
│   └── Loggable.php                  (Logging functionality)
├── Interfaces/                        (Contracts)
│   ├── ModelInterface.php            (Model contract)
│   ├── ServiceInterface.php          (Service contract)
│   ├── RepositoryInterface.php       (Repository contract)
│   ├── ValidatorInterface.php        (Validator contract)
│   └── TransformerInterface.php      (Transformer contract)
├── Enums/                             (Type-safe constants)
│   ├── JobStatus.php                 (Job status enum)
│   ├── ApplicationStatus.php         (Application status enum)
│   ├── CompanySize.php               (Company size enum)
│   ├── JobType.php                   (Job type enum)
│   └── UserRole.php                  (User role enum)
├── Exceptions/                        (Custom exceptions)
│   ├── PluginException.php           (Base plugin exception)
│   ├── DatabaseException.php         (Database errors)
│   ├── ValidationException.php       (Validation errors)
│   ├── ApiException.php              (API errors)
│   └── ServiceException.php          (Service errors)
└── Utils/                             (Utility classes)
    ├── Sanitizer.php                 (Data sanitization)
    ├── Validator.php                 (Data validation)
    ├── Logger.php                    (Logging utility)
    ├── Cache.php                     (Cache utility)
    └── Helper.php                    (General helpers)
```

## Admin Interface Structure
```
admin/
├── assets/                            (Admin assets)
│   ├── css/                          (Admin stylesheets)
│   │   ├── admin.css                 (Main admin styles)
│   │   ├── dashboard.css             (Dashboard styles)
│   │   └── forms.css                 (Form styles)
│   ├── js/                           (Admin JavaScript)
│   │   ├── admin.js                  (Main admin script)
│   │   ├── dashboard.js              (Dashboard functionality)
│   │   ├── forms.js                  (Form handling)
│   │   └── components/               (Admin components)
│   │       ├── DataTable.js          (Data table component)
│   │       ├── Modal.js              (Modal component)
│   │       └── Chart.js              (Chart component)
│   └── images/                       (Admin images)
├── views/                             (Admin templates)
│   ├── dashboard.php                 (Main dashboard)
│   ├── jobs/                         (Job management views)
│   │   ├── list.php                  (Job listing)
│   │   ├── edit.php                  (Job editing)
│   │   ├── create.php                (Job creation)
│   │   └── bulk-actions.php          (Bulk operations)
│   ├── companies/                    (Company management views)
│   │   ├── list.php                  (Company listing)
│   │   ├── edit.php                  (Company editing)
│   │   └── create.php                (Company creation)
│   ├── applications/                 (Application management)
│   │   ├── list.php                  (Application listing)
│   │   ├── view.php                  (Application details)
│   │   └── bulk-actions.php          (Bulk operations)
│   ├── settings/                     (Settings pages)
│   │   ├── general.php               (General settings)
│   │   ├── email.php                 (Email settings)
│   │   ├── seo.php                   (SEO settings)
│   │   └── advanced.php              (Advanced settings)
│   └── reports/                      (Reporting views)
│       ├── overview.php              (Overview report)
│       ├── jobs.php                  (Job statistics)
│       └── applications.php          (Application analytics)
└── partials/                          (Admin partials)
    ├── header.php                    (Admin header)
    ├── footer.php                    (Admin footer)
    ├── navigation.php                (Admin navigation)
    └── notifications.php             (Admin notifications)
```

## Frontend Templates Structure
```
templates/
├── jobs/                              (Job templates)
│   ├── archive-job.php               (Job listing page)
│   ├── single-job.php                (Single job page)
│   ├── job-search.php                (Job search page)
│   ├── job-application.php           (Application form)
│   └── partials/                     (Job partials)
│       ├── job-card.php              (Job card component)
│       ├── job-filters.php           (Filter component)
│       ├── job-meta.php              (Job metadata)
│       └── application-form.php      (Application form)
├── companies/                         (Company templates)
│   ├── archive-company.php           (Company listing)
│   ├── single-company.php            (Company profile)
│   └── partials/                     (Company partials)
│       ├── company-card.php          (Company card)
│       ├── company-info.php          (Company information)
│       └── company-jobs.php          (Company jobs)
├── categories/                        (Category templates)
│   ├── archive-category.php          (Category listing)
│   ├── single-category.php           (Category page)
│   └── partials/                     (Category partials)
│       └── category-card.php         (Category card)
├── shortcodes/                        (Shortcode templates)
│   ├── job-listing.php               (Job listing shortcode)
│   ├── job-search.php                (Search shortcode)
│   ├── company-listing.php           (Company listing shortcode)
│   └── featured-jobs.php             (Featured jobs shortcode)
└── emails/                            (Email templates)
    ├── job-alert.php                 (Job alert email)
    ├── application-received.php       (Application confirmation)
    ├── application-status.php         (Status update email)
    └── company-notification.php       (Company notification)
```

## Configuration Structure
```
config/
├── plugin.php                        (Main plugin config)
├── database.php                      (Database configuration)
├── api.php                           (API configuration)
├── email.php                         (Email settings)
├── cache.php                         (Cache configuration)
├── seo.php                           (SEO settings)
├── permissions.php                   (User permissions)
└── integrations/                     (Third-party integrations)
    ├── mailchimp.php                 (Mailchimp integration)
    ├── stripe.php                    (Stripe integration)
    └── google-analytics.php          (Analytics integration)
```

## Testing Structure
```
tests/
├── Unit/                             (Unit tests)
│   ├── Models/                       (Model tests)
│   ├── Services/                     (Service tests)
│   ├── Repositories/                 (Repository tests)
│   └── Utils/                        (Utility tests)
├── Integration/                      (Integration tests)
│   ├── API/                          (API tests)
│   ├── Database/                     (Database tests)
│   └── Frontend/                     (Frontend tests)
├── Feature/                          (Feature tests)
│   ├── JobManagementTest.php         (Job management features)
│   ├── ApplicationProcessTest.php    (Application process)
│   └── SearchFunctionalityTest.php   (Search features)
└── fixtures/                         (Test data)
    ├── jobs.json                     (Sample job data)
    ├── companies.json                (Sample company data)
    └── applications.json             (Sample application data)
```

## Key Features

### Advanced PHP Patterns
- **Repository Pattern**: Data access abstraction
- **Service Layer**: Business logic separation
- **Event-Driven Architecture**: Decoupled components
- **Dependency Injection**: Loose coupling
- **Command Pattern**: Action encapsulation

### Modern Database Design
- **Migration System**: Version-controlled schema
- **Seeder System**: Test data generation
- **Query Builder**: Fluent query interface
- **Connection Pooling**: Performance optimization
- **Transaction Management**: Data integrity

### REST API Architecture
- **Resource-based URLs**: RESTful design
- **HTTP Status Codes**: Proper response codes
- **Rate Limiting**: API protection
- **Authentication**: JWT/OAuth integration
- **Documentation**: OpenAPI/Swagger specs

### Performance Optimization
- **Query Optimization**: Efficient database queries
- **Caching Strategy**: Multi-layer caching
- **Lazy Loading**: On-demand data loading
- **Background Processing**: Async job handling
- **CDN Integration**: Asset optimization
