Universal System Architecture
Configuration-Driven Design

Base System → Configuration File → Specific Platform
     ↓              ↓                    ↓
Universal       Job Portal          Comparison Site
Components   →  config.json    →    Product Reviews

// config/platform-config.json
{
  "platform_type": "job-portal", // job-portal, comparison-site, forex-platform, etc.
  "site_name": "JobHub",
  "base_url": "https://example.com",
  "admin_url": "https://example.com/manager",
  
  "content_types": {
    "primary": {
      "name": "jobs",
      "singular": "job", 
      "slug_pattern": "{title}-{company}-{location}",
      "url_structure": "/jobs/{slug}.html",
      "fields": [
        {"name": "title", "type": "text", "required": true},
        {"name": "company", "type": "text", "required": true},
        {"name": "location", "type": "text", "required": true},
        {"name": "salary", "type": "text"},
        {"name": "description", "type": "wysiwyg"},
        {"name": "requirements", "type": "wysiwyg"}
      ]
    },
    "secondary": {
      "name": "companies", 
      "singular": "company",
      "url_structure": "/companies/{slug}.html",
      "fields": [
        {"name": "name", "type": "text", "required": true},
        {"name": "description", "type": "wysiwyg"},
        {"name": "website", "type": "url"},
        {"name": "logo", "type": "image"}
      ]
    }
  },
  
  "taxonomies": {
    "categories": {
      "name": "Categories",
      "slug": "categories", 
      "url_structure": "/categories/{slug}.html"
    },
    "locations": {
      "name": "Locations",
      "slug": "locations",
      "url_structure": "/locations/{slug}.html" 
    }
  },

  "layout": {
    "has_sidebar": true,
    "sidebar_components": ["latest_posts", "categories", "featured_items"],
    "header_menu": ["home", "browse", "categories", "about"],
    "footer_sections": ["links", "contact", "social"]
  }
}

