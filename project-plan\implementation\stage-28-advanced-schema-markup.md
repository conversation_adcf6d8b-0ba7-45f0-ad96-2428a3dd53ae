# Stage 28: Advanced Schema Markup

## Stage Overview
Implement comprehensive structured data following Google Search Central guidelines for job postings, organizations, breadcrumbs, and all supported rich results to maximize search visibility.

## Prerequisites
- Stage 27 completed successfully
- SEO optimization implemented
- Static site generation working
- Content management system operational

## Implementation Steps

### Step 28.1: Create Schema Markup Generator Service

#### manager/wp-content/themes/universal-theme/src/Services/SchemaMarkupService.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Theme\Services;

/**
 * Schema Markup Generator Service
 * 
 * Generates structured data following Google Search Central guidelines
 * 
 * @package UniversalApp\Theme\Services
 * @since 1.0.0
 */
final class SchemaMarkupService
{
    private string $siteUrl;
    private string $siteName;
    private array $organizationData;

    public function __construct()
    {
        $this->siteUrl = get_option('universal_static_site_url', home_url());
        $this->siteName = get_bloginfo('name');
        $this->loadOrganizationData();
    }

    /**
     * Generate complete schema markup for a page
     */
    public function generatePageSchema(array $pageData): string
    {
        $schemas = [];
        
        // Always include organization schema
        $schemas[] = $this->generateOrganizationSchema();
        
        // Add website schema for homepage
        if ($pageData['type'] === 'homepage') {
            $schemas[] = $this->generateWebsiteSchema();
        }
        
        // Add breadcrumb schema
        if (!empty($pageData['breadcrumbs'])) {
            $schemas[] = $this->generateBreadcrumbSchema($pageData['breadcrumbs']);
        }
        
        // Add page-specific schemas
        switch ($pageData['type']) {
            case 'job_posting':
                $schemas[] = $this->generateJobPostingSchema($pageData);
                break;
            case 'job_listing':
                $schemas[] = $this->generateJobListingSchema($pageData);
                break;
            case 'company':
                $schemas[] = $this->generateCompanySchema($pageData);
                break;
            case 'article':
                $schemas[] = $this->generateArticleSchema($pageData);
                break;
            case 'faq':
                $schemas[] = $this->generateFAQSchema($pageData);
                break;
        }
        
        return $this->wrapSchemaMarkup($schemas);
    }

    /**
     * Generate job posting schema (Google's most important for job sites)
     */
    private function generateJobPostingSchema(array $jobData): array
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'JobPosting',
            'title' => $jobData['title'],
            'description' => $this->cleanDescription($jobData['description']),
            'identifier' => [
                '@type' => 'PropertyValue',
                'name' => $this->siteName,
                'value' => $jobData['id']
            ],
            'datePosted' => $jobData['date_posted'],
            'hiringOrganization' => [
                '@type' => 'Organization',
                'name' => $jobData['company_name'],
                'sameAs' => $jobData['company_website'] ?? null,
                'logo' => $jobData['company_logo'] ?? null
            ],
            'jobLocation' => $this->generateJobLocationSchema($jobData['location']),
            'url' => $jobData['url']
        ];

        // Add optional properties
        if (!empty($jobData['valid_through'])) {
            $schema['validThrough'] = $jobData['valid_through'];
        }

        if (!empty($jobData['employment_type'])) {
            $schema['employmentType'] = strtoupper($jobData['employment_type']);
        }

        if (!empty($jobData['salary'])) {
            $schema['baseSalary'] = $this->generateSalarySchema($jobData['salary']);
        }

        if (!empty($jobData['remote'])) {
            $schema['jobLocationType'] = 'TELECOMMUTE';
            if (!empty($jobData['applicant_location_requirements'])) {
                $schema['applicantLocationRequirements'] = [
                    '@type' => 'Country',
                    'name' => $jobData['applicant_location_requirements']
                ];
            }
        }

        // Add education and experience requirements (beta)
        if (!empty($jobData['education_requirements'])) {
            $schema['educationRequirements'] = [
                '@type' => 'EducationalOccupationalCredential',
                'credentialCategory' => $jobData['education_requirements']
            ];
        }

        if (!empty($jobData['experience_months'])) {
            $schema['experienceRequirements'] = [
                '@type' => 'OccupationalExperienceRequirements',
                'monthsOfExperience' => $jobData['experience_months']
            ];
        }

        if (!empty($jobData['direct_apply'])) {
            $schema['directApply'] = true;
        }

        return $schema;
    }

    /**
     * Generate job location schema
     */
    private function generateJobLocationSchema(array $location): array
    {
        $locationSchema = [
            '@type' => 'Place',
            'address' => [
                '@type' => 'PostalAddress'
            ]
        ];

        if (!empty($location['street_address'])) {
            $locationSchema['address']['streetAddress'] = $location['street_address'];
        }

        if (!empty($location['locality'])) {
            $locationSchema['address']['addressLocality'] = $location['locality'];
        }

        if (!empty($location['region'])) {
            $locationSchema['address']['addressRegion'] = $location['region'];
        }

        if (!empty($location['postal_code'])) {
            $locationSchema['address']['postalCode'] = $location['postal_code'];
        }

        if (!empty($location['country'])) {
            $locationSchema['address']['addressCountry'] = $location['country'];
        }

        return $locationSchema;
    }

    /**
     * Generate salary schema
     */
    private function generateSalarySchema(array $salary): array
    {
        $salarySchema = [
            '@type' => 'MonetaryAmount',
            'currency' => $salary['currency'] ?? 'USD',
            'value' => [
                '@type' => 'QuantitativeValue',
                'unitText' => strtoupper($salary['unit'] ?? 'YEAR')
            ]
        ];

        if (!empty($salary['min']) && !empty($salary['max'])) {
            $salarySchema['value']['minValue'] = $salary['min'];
            $salarySchema['value']['maxValue'] = $salary['max'];
        } elseif (!empty($salary['value'])) {
            $salarySchema['value']['value'] = $salary['value'];
        }

        return $salarySchema;
    }

    /**
     * Generate organization schema
     */
    private function generateOrganizationSchema(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => $this->organizationData['name'],
            'url' => $this->siteUrl,
            'logo' => $this->organizationData['logo'],
            'description' => $this->organizationData['description'],
            'address' => $this->organizationData['address'] ?? null,
            'contactPoint' => $this->organizationData['contact_point'] ?? null,
            'sameAs' => $this->organizationData['social_profiles'] ?? []
        ];
    }

    /**
     * Generate website schema
     */
    private function generateWebsiteSchema(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => $this->siteName,
            'url' => $this->siteUrl,
            'description' => get_bloginfo('description'),
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => [
                    '@type' => 'EntryPoint',
                    'urlTemplate' => $this->siteUrl . '/search?q={search_term_string}'
                ],
                'query-input' => 'required name=search_term_string'
            ]
        ];
    }

    /**
     * Generate breadcrumb schema
     */
    private function generateBreadcrumbSchema(array $breadcrumbs): array
    {
        $listItems = [];
        
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $listItems[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url'] ?? null
            ];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $listItems
        ];
    }

    /**
     * Generate company schema
     */
    private function generateCompanySchema(array $companyData): array
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => $companyData['name'],
            'description' => $companyData['description'],
            'url' => $companyData['website'] ?? null,
            'logo' => $companyData['logo'] ?? null
        ];

        if (!empty($companyData['address'])) {
            $schema['address'] = [
                '@type' => 'PostalAddress',
                'streetAddress' => $companyData['address']['street'] ?? null,
                'addressLocality' => $companyData['address']['city'] ?? null,
                'addressRegion' => $companyData['address']['state'] ?? null,
                'postalCode' => $companyData['address']['zip'] ?? null,
                'addressCountry' => $companyData['address']['country'] ?? null
            ];
        }

        if (!empty($companyData['rating'])) {
            $schema['aggregateRating'] = [
                '@type' => 'AggregateRating',
                'ratingValue' => $companyData['rating']['value'],
                'reviewCount' => $companyData['rating']['count'],
                'bestRating' => 5,
                'worstRating' => 1
            ];
        }

        return $schema;
    }

    /**
     * Generate article schema
     */
    private function generateArticleSchema(array $articleData): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => $articleData['title'],
            'description' => $articleData['description'],
            'image' => $articleData['image'] ?? null,
            'author' => [
                '@type' => 'Person',
                'name' => $articleData['author'] ?? $this->siteName
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => $this->siteName,
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => $this->organizationData['logo']
                ]
            ],
            'datePublished' => $articleData['date_published'],
            'dateModified' => $articleData['date_modified'] ?? $articleData['date_published'],
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => $articleData['url']
            ]
        ];
    }

    /**
     * Generate FAQ schema
     */
    private function generateFAQSchema(array $faqData): array
    {
        $questions = [];
        
        foreach ($faqData['questions'] as $qa) {
            $questions[] = [
                '@type' => 'Question',
                'name' => $qa['question'],
                'acceptedAnswer' => [
                    '@type' => 'Answer',
                    'text' => $qa['answer']
                ]
            ];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'FAQPage',
            'mainEntity' => $questions
        ];
    }

    /**
     * Generate job listing schema for search results pages
     */
    private function generateJobListingSchema(array $listingData): array
    {
        $jobs = [];
        
        foreach ($listingData['jobs'] as $job) {
            $jobs[] = $this->generateJobPostingSchema($job);
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'ItemList',
            'itemListElement' => array_map(function($job, $index) {
                return [
                    '@type' => 'ListItem',
                    'position' => $index + 1,
                    'item' => $job
                ];
            }, $jobs, array_keys($jobs))
        ];
    }

    /**
     * Wrap schema markup in JSON-LD script tag
     */
    private function wrapSchemaMarkup(array $schemas): string
    {
        $jsonLd = json_encode($schemas, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        
        return sprintf(
            '<script type="application/ld+json">%s</script>',
            $jsonLd
        );
    }

    /**
     * Clean description for schema markup
     */
    private function cleanDescription(string $description): string
    {
        // Remove HTML tags
        $description = strip_tags($description);
        
        // Remove extra whitespace
        $description = preg_replace('/\s+/', ' ', $description);
        
        // Trim to reasonable length
        if (strlen($description) > 5000) {
            $description = substr($description, 0, 4997) . '...';
        }
        
        return trim($description);
    }

    /**
     * Load organization data from WordPress options
     */
    private function loadOrganizationData(): void
    {
        $this->organizationData = get_option('universal_organization_schema', [
            'name' => $this->siteName,
            'description' => get_bloginfo('description'),
            'logo' => $this->siteUrl . '/assets/images/logo.png',
            'address' => null,
            'contact_point' => null,
            'social_profiles' => []
        ]);
    }
}
```

### Step 28.2: Create Schema Validation Service

#### manager/wp-content/themes/universal-theme/src/Services/SchemaValidationService.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Theme\Services;

/**
 * Schema Validation Service
 * 
 * Validates structured data against Google's requirements
 * 
 * @package UniversalApp\Theme\Services
 * @since 1.0.0
 */
final class SchemaValidationService
{
    private array $validationRules;

    public function __construct()
    {
        $this->loadValidationRules();
    }

    /**
     * Validate job posting schema
     */
    public function validateJobPosting(array $schema): array
    {
        $errors = [];
        $warnings = [];

        // Required fields validation
        $requiredFields = ['title', 'description', 'datePosted', 'hiringOrganization', 'jobLocation'];
        
        foreach ($requiredFields as $field) {
            if (empty($schema[$field])) {
                $errors[] = "Missing required field: {$field}";
            }
        }

        // Title validation
        if (!empty($schema['title'])) {
            if (strlen($schema['title']) > 100) {
                $warnings[] = 'Job title is longer than recommended 100 characters';
            }
            
            if (preg_match('/[!*]{3,}/', $schema['title'])) {
                $errors[] = 'Job title contains excessive special characters';
            }
        }

        // Description validation
        if (!empty($schema['description'])) {
            if (strlen($schema['description']) < 100) {
                $warnings[] = 'Job description is shorter than recommended 100 characters';
            }
            
            if (strip_tags($schema['description']) !== $schema['description']) {
                $errors[] = 'Job description contains HTML tags';
            }
        }

        // Date validation
        if (!empty($schema['datePosted'])) {
            if (!$this->isValidISO8601Date($schema['datePosted'])) {
                $errors[] = 'datePosted must be in ISO 8601 format';
            }
        }

        // Hiring organization validation
        if (!empty($schema['hiringOrganization'])) {
            if (empty($schema['hiringOrganization']['name'])) {
                $errors[] = 'hiringOrganization must have a name';
            }
        }

        // Job location validation
        if (!empty($schema['jobLocation'])) {
            $locationErrors = $this->validateJobLocation($schema['jobLocation']);
            $errors = array_merge($errors, $locationErrors);
        }

        // Remote job validation
        if (!empty($schema['jobLocationType']) && $schema['jobLocationType'] === 'TELECOMMUTE') {
            if (empty($schema['applicantLocationRequirements']) && empty($schema['jobLocation'])) {
                $errors[] = 'Remote jobs must specify applicantLocationRequirements or jobLocation';
            }
        }

        // Salary validation
        if (!empty($schema['baseSalary'])) {
            $salaryErrors = $this->validateSalary($schema['baseSalary']);
            $errors = array_merge($errors, $salaryErrors);
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    /**
     * Validate job location
     */
    private function validateJobLocation(array $location): array
    {
        $errors = [];

        if (empty($location['address'])) {
            $errors[] = 'jobLocation must have an address';
            return $errors;
        }

        $address = $location['address'];
        
        if (empty($address['addressLocality']) && empty($address['addressRegion'])) {
            $errors[] = 'Job location must have addressLocality or addressRegion';
        }

        return $errors;
    }

    /**
     * Validate salary schema
     */
    private function validateSalary(array $salary): array
    {
        $errors = [];

        if (empty($salary['currency'])) {
            $errors[] = 'baseSalary must specify currency';
        }

        if (empty($salary['value'])) {
            $errors[] = 'baseSalary must have a value';
            return $errors;
        }

        $value = $salary['value'];
        
        if (empty($value['unitText'])) {
            $errors[] = 'baseSalary value must specify unitText';
        } else {
            $validUnits = ['HOUR', 'DAY', 'WEEK', 'MONTH', 'YEAR'];
            if (!in_array($value['unitText'], $validUnits)) {
                $errors[] = 'baseSalary unitText must be one of: ' . implode(', ', $validUnits);
            }
        }

        if (empty($value['value']) && (empty($value['minValue']) || empty($value['maxValue']))) {
            $errors[] = 'baseSalary must specify either value or minValue/maxValue';
        }

        return $errors;
    }

    /**
     * Validate organization schema
     */
    public function validateOrganization(array $schema): array
    {
        $errors = [];
        $warnings = [];

        // Required fields
        if (empty($schema['name'])) {
            $errors[] = 'Organization must have a name';
        }

        if (empty($schema['url'])) {
            $warnings[] = 'Organization should have a URL';
        }

        if (empty($schema['logo'])) {
            $warnings[] = 'Organization should have a logo for better rich results';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    /**
     * Validate breadcrumb schema
     */
    public function validateBreadcrumb(array $schema): array
    {
        $errors = [];
        $warnings = [];

        if (empty($schema['itemListElement'])) {
            $errors[] = 'BreadcrumbList must have itemListElement';
            return ['valid' => false, 'errors' => $errors, 'warnings' => $warnings];
        }

        foreach ($schema['itemListElement'] as $index => $item) {
            if (empty($item['position'])) {
                $errors[] = "Breadcrumb item {$index} must have position";
            }
            
            if (empty($item['name'])) {
                $errors[] = "Breadcrumb item {$index} must have name";
            }
            
            if ($index < count($schema['itemListElement']) - 1 && empty($item['item'])) {
                $warnings[] = "Breadcrumb item {$index} should have item URL";
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    /**
     * Check if date is valid ISO 8601 format
     */
    private function isValidISO8601Date(string $date): bool
    {
        $formats = [
            'Y-m-d',
            'Y-m-d\TH:i:s',
            'Y-m-d\TH:i:sP',
            'Y-m-d\TH:i:s\Z'
        ];

        foreach ($formats as $format) {
            $dateTime = \DateTime::createFromFormat($format, $date);
            if ($dateTime && $dateTime->format($format) === $date) {
                return true;
            }
        }

        return false;
    }

    /**
     * Load validation rules from configuration
     */
    private function loadValidationRules(): void
    {
        $this->validationRules = [
            'job_posting' => [
                'required' => ['title', 'description', 'datePosted', 'hiringOrganization', 'jobLocation'],
                'recommended' => ['validThrough', 'employmentType', 'baseSalary'],
                'title_max_length' => 100,
                'description_min_length' => 100
            ],
            'organization' => [
                'required' => ['name'],
                'recommended' => ['url', 'logo', 'description']
            ]
        ];
    }
}
```

### Step 28.3: Create Schema Testing and Monitoring

#### src/js/modules/SchemaMonitor.js
```javascript
/**
 * Schema Markup Monitor
 * 
 * Monitors and validates schema markup on the client side
 * 
 * @package UniversalApp
 * @since 1.0.0
 */
class SchemaMonitor {
    constructor() {
        this.schemas = [];
        this.validationResults = [];
        
        this.init();
    }

    /**
     * Initialize schema monitoring
     */
    init() {
        // Extract all JSON-LD schemas
        this.extractSchemas();
        
        // Validate schemas
        this.validateSchemas();
        
        // Monitor for new schemas (for dynamic content)
        this.setupSchemaObserver();
        
        // Send validation results to analytics
        this.reportValidationResults();
    }

    /**
     * Extract all JSON-LD schemas from the page
     */
    extractSchemas() {
        const scriptTags = document.querySelectorAll('script[type="application/ld+json"]');
        
        scriptTags.forEach((script, index) => {
            try {
                const schema = JSON.parse(script.textContent);
                this.schemas.push({
                    index: index,
                    element: script,
                    data: schema,
                    type: this.getSchemaType(schema)
                });
            } catch (error) {
                console.error('Invalid JSON-LD schema at index', index, error);
                this.validationResults.push({
                    index: index,
                    valid: false,
                    errors: ['Invalid JSON syntax: ' + error.message]
                });
            }
        });
    }

    /**
     * Get schema type from schema data
     */
    getSchemaType(schema) {
        if (Array.isArray(schema)) {
            return schema.map(s => s['@type']).join(', ');
        }
        return schema['@type'] || 'Unknown';
    }

    /**
     * Validate extracted schemas
     */
    validateSchemas() {
        this.schemas.forEach((schema, index) => {
            const validation = this.validateSchema(schema.data, schema.type);
            this.validationResults.push({
                index: index,
                type: schema.type,
                valid: validation.valid,
                errors: validation.errors,
                warnings: validation.warnings
            });
        });
    }

    /**
     * Validate individual schema
     */
    validateSchema(schemaData, schemaType) {
        const errors = [];
        const warnings = [];

        // Handle array of schemas
        if (Array.isArray(schemaData)) {
            schemaData.forEach((schema, index) => {
                const result = this.validateSingleSchema(schema);
                if (result.errors.length > 0) {
                    errors.push(...result.errors.map(e => `Schema ${index}: ${e}`));
                }
                if (result.warnings.length > 0) {
                    warnings.push(...result.warnings.map(w => `Schema ${index}: ${w}`));
                }
            });
        } else {
            const result = this.validateSingleSchema(schemaData);
            errors.push(...result.errors);
            warnings.push(...result.warnings);
        }

        return {
            valid: errors.length === 0,
            errors: errors,
            warnings: warnings
        };
    }

    /**
     * Validate single schema object
     */
    validateSingleSchema(schema) {
        const errors = [];
        const warnings = [];

        // Check for required @context and @type
        if (!schema['@context']) {
            errors.push('Missing @context property');
        }

        if (!schema['@type']) {
            errors.push('Missing @type property');
        }

        // Type-specific validation
        switch (schema['@type']) {
            case 'JobPosting':
                const jobValidation = this.validateJobPosting(schema);
                errors.push(...jobValidation.errors);
                warnings.push(...jobValidation.warnings);
                break;
                
            case 'Organization':
                const orgValidation = this.validateOrganization(schema);
                errors.push(...orgValidation.errors);
                warnings.push(...orgValidation.warnings);
                break;
                
            case 'BreadcrumbList':
                const breadcrumbValidation = this.validateBreadcrumbList(schema);
                errors.push(...breadcrumbValidation.errors);
                warnings.push(...breadcrumbValidation.warnings);
                break;
        }

        return { errors, warnings };
    }

    /**
     * Validate JobPosting schema
     */
    validateJobPosting(schema) {
        const errors = [];
        const warnings = [];

        // Required fields
        const requiredFields = ['title', 'description', 'datePosted', 'hiringOrganization', 'jobLocation'];
        requiredFields.forEach(field => {
            if (!schema[field]) {
                errors.push(`Missing required field: ${field}`);
            }
        });

        // Title validation
        if (schema.title && schema.title.length > 100) {
            warnings.push('Job title exceeds recommended 100 characters');
        }

        // Description validation
        if (schema.description && schema.description.length < 100) {
            warnings.push('Job description is shorter than recommended 100 characters');
        }

        // Date validation
        if (schema.datePosted && !this.isValidDate(schema.datePosted)) {
            errors.push('datePosted is not in valid ISO 8601 format');
        }

        return { errors, warnings };
    }

    /**
     * Validate Organization schema
     */
    validateOrganization(schema) {
        const errors = [];
        const warnings = [];

        if (!schema.name) {
            errors.push('Organization missing required name field');
        }

        if (!schema.url) {
            warnings.push('Organization should include URL');
        }

        if (!schema.logo) {
            warnings.push('Organization should include logo for better rich results');
        }

        return { errors, warnings };
    }

    /**
     * Validate BreadcrumbList schema
     */
    validateBreadcrumbList(schema) {
        const errors = [];
        const warnings = [];

        if (!schema.itemListElement || !Array.isArray(schema.itemListElement)) {
            errors.push('BreadcrumbList missing itemListElement array');
            return { errors, warnings };
        }

        schema.itemListElement.forEach((item, index) => {
            if (!item.position) {
                errors.push(`Breadcrumb item ${index} missing position`);
            }
            
            if (!item.name) {
                errors.push(`Breadcrumb item ${index} missing name`);
            }
        });

        return { errors, warnings };
    }

    /**
     * Check if date is valid ISO 8601 format
     */
    isValidDate(dateString) {
        const iso8601Regex = /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(\.\d{3})?([+-]\d{2}:\d{2}|Z)?)?$/;
        return iso8601Regex.test(dateString);
    }

    /**
     * Setup observer for dynamically added schemas
     */
    setupSchemaObserver() {
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const newSchemas = node.querySelectorAll('script[type="application/ld+json"]');
                        if (newSchemas.length > 0) {
                            this.extractSchemas();
                            this.validateSchemas();
                        }
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * Report validation results to analytics
     */
    reportValidationResults() {
        const totalSchemas = this.validationResults.length;
        const validSchemas = this.validationResults.filter(r => r.valid).length;
        const invalidSchemas = totalSchemas - validSchemas;

        // Send to Google Analytics
        if (window.gtag) {
            gtag('event', 'schema_validation', {
                event_category: 'SEO',
                event_label: 'Schema Markup',
                custom_map: {
                    total_schemas: totalSchemas,
                    valid_schemas: validSchemas,
                    invalid_schemas: invalidSchemas
                }
            });
        }

        // Log validation results in development
        if (process.env.NODE_ENV === 'development') {
            console.group('Schema Validation Results');
            console.log(`Total schemas: ${totalSchemas}`);
            console.log(`Valid schemas: ${validSchemas}`);
            console.log(`Invalid schemas: ${invalidSchemas}`);
            
            this.validationResults.forEach(result => {
                if (!result.valid) {
                    console.error(`Schema ${result.index} (${result.type}):`, result.errors);
                }
                if (result.warnings && result.warnings.length > 0) {
                    console.warn(`Schema ${result.index} (${result.type}):`, result.warnings);
                }
            });
            console.groupEnd();
        }
    }

    /**
     * Get validation summary
     */
    getValidationSummary() {
        return {
            total: this.validationResults.length,
            valid: this.validationResults.filter(r => r.valid).length,
            invalid: this.validationResults.filter(r => !r.valid).length,
            results: this.validationResults
        };
    }
}

// Initialize schema monitor
const schemaMonitor = new SchemaMonitor();

// Export for global access
window.SchemaMonitor = schemaMonitor;

export default SchemaMonitor;
```

## Testing Checklist

### Schema Implementation
- [ ] Job posting schema includes all required fields
- [ ] Organization schema is complete
- [ ] Breadcrumb schema is properly structured
- [ ] Website schema includes search action
- [ ] All schemas validate in Rich Results Test
- [ ] JSON-LD syntax is valid

### Google Requirements Compliance
- [ ] Job postings follow Google's content policies
- [ ] Salary information matches page content
- [ ] Company information is accurate
- [ ] Remote job requirements are met
- [ ] Education/experience requirements are properly formatted
- [ ] Direct apply functionality is correctly marked

### Validation and Monitoring
- [ ] Schema validation service works correctly
- [ ] Client-side monitoring detects issues
- [ ] Validation results are tracked in analytics
- [ ] Error reporting is functional
- [ ] Rich Results Test shows no errors

## Success Criteria
1. ✅ Comprehensive schema markup implemented
2. ✅ Google Search Central compliance achieved
3. ✅ Rich Results Test validation passes
4. ✅ Schema monitoring system operational
5. ✅ Job posting rich results appear in search
6. ✅ Organization knowledge panel displays
7. ✅ All files under 150 lines
8. ✅ Live testing passes

## Next Stage
Once Stage 28 is complete and all tests pass, proceed to **Stage 29: Build Pipeline & Deployment** where we'll implement automated build processes, CDN integration, and production deployment systems.
