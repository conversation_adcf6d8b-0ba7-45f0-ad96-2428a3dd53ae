# Dynamic Islands (React Components)

## Dynamic Islands Philosophy

### Progressive Enhancement
Dynamic islands are React components that enhance static HTML with interactive functionality. They hydrate specific sections of the page while leaving the core content as static HTML.

### Island Architecture Benefits
- **Selective Hydration**: Only interactive components load JavaScript
- **Performance**: Minimal JavaScript payload for core functionality
- **Resilience**: Page works without JavaScript
- **SEO**: Static content is immediately crawlable
- **User Experience**: Fast initial load with progressive interactivity

## Core Dynamic Components

### User Authentication Widget
```jsx
// components/AuthWidget.jsx
import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';

const AuthWidget = () => {
  const { user, login, logout, isLoading } = useAuth();
  const [showDropdown, setShowDropdown] = useState(false);

  if (isLoading) {
    return (
      <div className="auth-widget">
        <div className="skeleton skeleton-text" style={{ width: '100px' }}></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="auth-widget">
        <button 
          onClick={login}
          className="btn btn--primary btn--small"
          aria-label="Sign in to your account"
        >
          Sign In
        </button>
      </div>
    );
  }

  return (
    <div className="auth-widget">
      <div className="user-menu">
        <button
          onClick={() => setShowDropdown(!showDropdown)}
          className="user-menu__trigger"
          aria-expanded={showDropdown}
          aria-haspopup="true"
          aria-label="User menu"
        >
          <img 
            src={user.avatar || '/assets/images/default-avatar.png'} 
            alt={user.name}
            className="user-menu__avatar"
          />
          <span className="user-menu__name">{user.name}</span>
          <svg className="user-menu__icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7 10l5 5 5-5z"/>
          </svg>
        </button>
        
        {showDropdown && (
          <div className="user-menu__dropdown" role="menu">
            <a href="/dashboard/" className="user-menu__item" role="menuitem">
              <svg className="user-menu__item-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
              </svg>
              Dashboard
            </a>
            <a href="/profile/" className="user-menu__item" role="menuitem">
              <svg className="user-menu__item-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
              </svg>
              Profile
            </a>
            <a href="/saved-jobs/" className="user-menu__item" role="menuitem">
              <svg className="user-menu__item-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17 3H7c-1.1 0-1.99.9-1.99 2L5 21l7-3 7 3V5c0-1.1-.9-2-2-2z"/>
              </svg>
              Saved Jobs
            </a>
            <hr className="user-menu__separator" />
            <button 
              onClick={logout}
              className="user-menu__item user-menu__item--button"
              role="menuitem"
            >
              <svg className="user-menu__item-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
              </svg>
              Sign Out
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthWidget;
```

### Search Functionality
```jsx
// components/SearchWidget.jsx
import React, { useState, useEffect, useRef } from 'react';
import { useDebounce } from '../hooks/useDebounce';
import { searchAPI } from '../services/api';

const SearchWidget = ({ placeholder = "Search jobs..." }) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const debouncedQuery = useDebounce(query, 300);
  const searchRef = useRef(null);

  useEffect(() => {
    if (debouncedQuery.length >= 2) {
      fetchSuggestions(debouncedQuery);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [debouncedQuery]);

  const fetchSuggestions = async (searchQuery) => {
    setIsLoading(true);
    try {
      const results = await searchAPI.getSuggestions(searchQuery);
      setSuggestions(results);
      setShowSuggestions(true);
    } catch (error) {
      console.error('Search suggestions failed:', error);
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (query.trim()) {
      window.location.href = `/search/?q=${encodeURIComponent(query.trim())}`;
    }
  };

  const handleSuggestionClick = (suggestion) => {
    setQuery(suggestion.title);
    setShowSuggestions(false);
    window.location.href = suggestion.url;
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  return (
    <div className="search-widget" ref={searchRef}>
      <form onSubmit={handleSubmit} className="search-widget__form">
        <div className="search-widget__input-container">
          <input
            type="search"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="search-widget__input"
            aria-label="Search"
            aria-autocomplete="list"
            aria-expanded={showSuggestions}
            role="combobox"
          />
          <button 
            type="submit" 
            className="search-widget__button"
            aria-label="Submit search"
          >
            {isLoading ? (
              <div className="spinner" aria-hidden="true"></div>
            ) : (
              <svg className="search-widget__icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            )}
          </button>
        </div>
        
        {showSuggestions && suggestions.length > 0 && (
          <div className="search-widget__suggestions" role="listbox">
            {suggestions.map((suggestion, index) => (
              <button
                key={suggestion.id}
                onClick={() => handleSuggestionClick(suggestion)}
                className="search-widget__suggestion"
                role="option"
                aria-selected={false}
              >
                <div className="search-widget__suggestion-content">
                  <span className="search-widget__suggestion-title">
                    {suggestion.title}
                  </span>
                  <span className="search-widget__suggestion-type">
                    {suggestion.type}
                  </span>
                </div>
                {suggestion.company && (
                  <span className="search-widget__suggestion-company">
                    {suggestion.company}
                  </span>
                )}
              </button>
            ))}
          </div>
        )}
      </form>
    </div>
  );
};

export default SearchWidget;
```

### Job Application Form
```jsx
// components/JobApplicationForm.jsx
import React, { useState } from 'react';
import { useForm } from '../hooks/useForm';
import { applicationAPI } from '../services/api';

const JobApplicationForm = ({ jobId, jobTitle, companyName }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);
  
  const { values, errors, handleChange, handleSubmit, isValid } = useForm({
    initialValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      coverLetter: '',
      resume: null,
      linkedinUrl: '',
      portfolioUrl: ''
    },
    validationRules: {
      firstName: { required: true, minLength: 2 },
      lastName: { required: true, minLength: 2 },
      email: { required: true, email: true },
      phone: { required: true, phone: true },
      coverLetter: { required: true, minLength: 100 },
      resume: { required: true, fileType: ['pdf', 'doc', 'docx'] }
    }
  });

  const onSubmit = async (formData) => {
    setIsSubmitting(true);
    setSubmitStatus(null);
    
    try {
      const applicationData = new FormData();
      Object.keys(formData).forEach(key => {
        if (formData[key] !== null && formData[key] !== '') {
          applicationData.append(key, formData[key]);
        }
      });
      applicationData.append('jobId', jobId);
      
      await applicationAPI.submitApplication(applicationData);
      setSubmitStatus('success');
    } catch (error) {
      setSubmitStatus('error');
      console.error('Application submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitStatus === 'success') {
    return (
      <div className="application-form__success">
        <div className="success-message">
          <svg className="success-message__icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
          </svg>
          <h3 className="success-message__title">Application Submitted!</h3>
          <p className="success-message__text">
            Your application for <strong>{jobTitle}</strong> at <strong>{companyName}</strong> 
            has been successfully submitted. You'll hear back from the hiring team soon.
          </p>
          <button 
            onClick={() => window.location.href = '/dashboard/applications/'}
            className="btn btn--primary"
          >
            View My Applications
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="application-form">
      <div className="application-form__header">
        <h3 className="application-form__title">Apply for {jobTitle}</h3>
        <p className="application-form__subtitle">at {companyName}</p>
      </div>
      
      <form onSubmit={handleSubmit(onSubmit)} className="application-form__form">
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="firstName" className="form__label form__label--required">
              First Name
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={values.firstName}
              onChange={handleChange}
              className={`form__input ${errors.firstName ? 'form__input--error' : ''}`}
              required
            />
            {errors.firstName && (
              <span className="form__error">{errors.firstName}</span>
            )}
          </div>
          
          <div className="form-group">
            <label htmlFor="lastName" className="form__label form__label--required">
              Last Name
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={values.lastName}
              onChange={handleChange}
              className={`form__input ${errors.lastName ? 'form__input--error' : ''}`}
              required
            />
            {errors.lastName && (
              <span className="form__error">{errors.lastName}</span>
            )}
          </div>
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="email" className="form__label form__label--required">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={values.email}
              onChange={handleChange}
              className={`form__input ${errors.email ? 'form__input--error' : ''}`}
              required
            />
            {errors.email && (
              <span className="form__error">{errors.email}</span>
            )}
          </div>
          
          <div className="form-group">
            <label htmlFor="phone" className="form__label form__label--required">
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={values.phone}
              onChange={handleChange}
              className={`form__input ${errors.phone ? 'form__input--error' : ''}`}
              required
            />
            {errors.phone && (
              <span className="form__error">{errors.phone}</span>
            )}
          </div>
        </div>
        
        <div className="form-group">
          <label htmlFor="resume" className="form__label form__label--required">
            Resume
          </label>
          <input
            type="file"
            id="resume"
            name="resume"
            onChange={handleChange}
            accept=".pdf,.doc,.docx"
            className={`form__input ${errors.resume ? 'form__input--error' : ''}`}
            required
          />
          <span className="form__help">
            Accepted formats: PDF, DOC, DOCX (max 5MB)
          </span>
          {errors.resume && (
            <span className="form__error">{errors.resume}</span>
          )}
        </div>
        
        <div className="form-group">
          <label htmlFor="coverLetter" className="form__label form__label--required">
            Cover Letter
          </label>
          <textarea
            id="coverLetter"
            name="coverLetter"
            value={values.coverLetter}
            onChange={handleChange}
            rows="6"
            className={`form__textarea ${errors.coverLetter ? 'form__textarea--error' : ''}`}
            placeholder="Tell us why you're interested in this position and what makes you a great fit..."
            required
          />
          <span className="form__help">
            Minimum 100 characters ({values.coverLetter.length}/100)
          </span>
          {errors.coverLetter && (
            <span className="form__error">{errors.coverLetter}</span>
          )}
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="linkedinUrl" className="form__label">
              LinkedIn Profile (Optional)
            </label>
            <input
              type="url"
              id="linkedinUrl"
              name="linkedinUrl"
              value={values.linkedinUrl}
              onChange={handleChange}
              className="form__input"
              placeholder="https://linkedin.com/in/yourprofile"
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="portfolioUrl" className="form__label">
              Portfolio URL (Optional)
            </label>
            <input
              type="url"
              id="portfolioUrl"
              name="portfolioUrl"
              value={values.portfolioUrl}
              onChange={handleChange}
              className="form__input"
              placeholder="https://yourportfolio.com"
            />
          </div>
        </div>
        
        {submitStatus === 'error' && (
          <div className="form__error-message">
            <svg className="form__error-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            There was an error submitting your application. Please try again.
          </div>
        )}
        
        <div className="form-actions">
          <button
            type="submit"
            disabled={!isValid || isSubmitting}
            className="btn btn--primary btn--large btn--full"
          >
            {isSubmitting ? (
              <>
                <div className="spinner" aria-hidden="true"></div>
                Submitting Application...
              </>
            ) : (
              'Submit Application'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default JobApplicationForm;
```

### Save Job Widget
```jsx
// components/SaveJobWidget.jsx
import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { savedJobsAPI } from '../services/api';

const SaveJobWidget = ({ jobId, className = '' }) => {
  const { user } = useAuth();
  const [isSaved, setIsSaved] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (user && jobId) {
      checkSavedStatus();
    }
  }, [user, jobId]);

  const checkSavedStatus = async () => {
    try {
      const saved = await savedJobsAPI.isSaved(jobId);
      setIsSaved(saved);
    } catch (error) {
      console.error('Failed to check saved status:', error);
    }
  };

  const handleToggleSave = async () => {
    if (!user) {
      // Redirect to login
      window.location.href = '/login/?redirect=' + encodeURIComponent(window.location.pathname);
      return;
    }

    setIsLoading(true);
    try {
      if (isSaved) {
        await savedJobsAPI.unsaveJob(jobId);
        setIsSaved(false);
      } else {
        await savedJobsAPI.saveJob(jobId);
        setIsSaved(true);
      }
    } catch (error) {
      console.error('Failed to toggle save status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleToggleSave}
      disabled={isLoading}
      className={`save-job-widget ${className} ${isSaved ? 'save-job-widget--saved' : ''}`}
      aria-label={isSaved ? 'Remove from saved jobs' : 'Save job'}
      title={isSaved ? 'Remove from saved jobs' : 'Save job'}
    >
      {isLoading ? (
        <div className="spinner spinner--small" aria-hidden="true"></div>
      ) : (
        <svg className="save-job-widget__icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M17 3H7c-1.1 0-1.99.9-1.99 2L5 21l7-3 7 3V5c0-1.1-.9-2-2-2z"/>
        </svg>
      )}
      <span className="save-job-widget__text">
        {isSaved ? 'Saved' : 'Save'}
      </span>
    </button>
  );
};

export default SaveJobWidget;
```

## Notification System

### Real-time Notifications
```jsx
// components/NotificationCenter.jsx
import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { notificationAPI } from '../services/api';

const NotificationCenter = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [showDropdown, setShowDropdown] = useState(false);

  useEffect(() => {
    if (user) {
      fetchNotifications();
      // Set up real-time updates
      const interval = setInterval(fetchNotifications, 30000); // Poll every 30 seconds
      return () => clearInterval(interval);
    }
  }, [user]);

  const fetchNotifications = async () => {
    try {
      const data = await notificationAPI.getNotifications();
      setNotifications(data.notifications);
      setUnreadCount(data.unreadCount);
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      await notificationAPI.markAsRead(notificationId);
      setNotifications(prev => 
        prev.map(notif => 
          notif.id === notificationId 
            ? { ...notif, read: true }
            : notif
        )
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      await notificationAPI.markAllAsRead();
      setNotifications(prev => prev.map(notif => ({ ...notif, read: true })));
      setUnreadCount(0);
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  if (!user) return null;

  return (
    <div className="notification-center">
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className="notification-center__trigger"
        aria-expanded={showDropdown}
        aria-label={`Notifications ${unreadCount > 0 ? `(${unreadCount} unread)` : ''}`}
      >
        <svg className="notification-center__icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
        </svg>
        {unreadCount > 0 && (
          <span className="notification-center__badge" aria-hidden="true">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {showDropdown && (
        <div className="notification-center__dropdown">
          <div className="notification-center__header">
            <h3 className="notification-center__title">Notifications</h3>
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="notification-center__mark-all"
              >
                Mark all as read
              </button>
            )}
          </div>

          <div className="notification-center__list">
            {notifications.length === 0 ? (
              <div className="notification-center__empty">
                <p>No notifications yet</p>
              </div>
            ) : (
              notifications.map(notification => (
                <div
                  key={notification.id}
                  className={`notification-item ${!notification.read ? 'notification-item--unread' : ''}`}
                  onClick={() => !notification.read && markAsRead(notification.id)}
                >
                  <div className="notification-item__content">
                    <h4 className="notification-item__title">
                      {notification.title}
                    </h4>
                    <p className="notification-item__message">
                      {notification.message}
                    </p>
                    <time className="notification-item__time">
                      {notification.timeAgo}
                    </time>
                  </div>
                  {!notification.read && (
                    <div className="notification-item__unread-indicator" aria-hidden="true"></div>
                  )}
                </div>
              ))
            )}
          </div>

          <div className="notification-center__footer">
            <a href="/notifications/" className="notification-center__view-all">
              View all notifications
            </a>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationCenter;
```
