# Performance Optimization and Asset Management

## Performance Strategy

### Core Performance Goals
- **First Contentful Paint (FCP)**: < 1.5 seconds
- **Largest Contentful Paint (LCP)**: < 2.5 seconds
- **Cumulative Layout Shift (CLS)**: < 0.1
- **First Input Delay (FID)**: < 100 milliseconds
- **Time to Interactive (TTI)**: < 3.5 seconds

### Performance Budget
```javascript
// performance-budget.js
export const performanceBudget = {
  // Asset size limits
  assets: {
    totalCSS: 50 * 1024,      // 50KB
    totalJS: 150 * 1024,      // 150KB
    criticalCSS: 14 * 1024,   // 14KB (inlined)
    images: 500 * 1024,       // 500KB per page
    fonts: 100 * 1024        // 100KB total
  },
  
  // Performance metrics
  metrics: {
    fcp: 1500,    // 1.5s
    lcp: 2500,    // 2.5s
    fid: 100,     // 100ms
    cls: 0.1,     // 0.1
    tti: 3500     // 3.5s
  },
  
  // Resource counts
  resources: {
    totalRequests: 50,
    criticalRequests: 10,
    thirdPartyRequests: 5
  }
};
```

## Asset Optimization

### Image Optimization Strategy
```javascript
// scripts/image-optimization.js
import sharp from 'sharp';
import imagemin from 'imagemin';
import imageminWebp from 'imagemin-webp';
import imageminAvif from 'imagemin-avif';

class AdvancedImageOptimizer {
  constructor(config) {
    this.config = config;
    this.formats = ['webp', 'avif', 'jpg'];
    this.breakpoints = [320, 640, 768, 1024, 1280, 1920];
  }

  async optimizeImage(inputPath, outputDir, options = {}) {
    const { quality = 85, progressive = true } = options;
    const filename = path.basename(inputPath, path.extname(inputPath));
    
    // Generate responsive images for each breakpoint
    const responsiveImages = await Promise.all(
      this.breakpoints.map(width => 
        this.generateResponsiveImage(inputPath, outputDir, filename, width, quality)
      )
    );

    // Generate modern formats
    const modernFormats = await Promise.all(
      this.formats.map(format => 
        this.generateModernFormat(inputPath, outputDir, filename, format, quality)
      )
    );

    // Generate blur placeholder
    const placeholder = await this.generateBlurPlaceholder(inputPath);

    return {
      responsive: responsiveImages,
      modern: modernFormats,
      placeholder
    };
  }

  async generateResponsiveImage(inputPath, outputDir, filename, width, quality) {
    const outputPath = path.join(outputDir, `${filename}-${width}w.jpg`);
    
    const metadata = await sharp(inputPath)
      .resize(width, null, { 
        withoutEnlargement: true,
        fastShrinkOnLoad: false
      })
      .jpeg({ 
        quality, 
        progressive: true,
        mozjpeg: true
      })
      .toFile(outputPath);

    return {
      width,
      path: outputPath,
      size: metadata.size
    };
  }

  async generateModernFormat(inputPath, outputDir, filename, format, quality) {
    const plugins = {
      webp: imageminWebp({ quality, method: 6 }),
      avif: imageminAvif({ quality: quality - 10, speed: 2 })
    };

    if (!plugins[format]) return null;

    const result = await imagemin([inputPath], {
      destination: outputDir,
      plugins: [plugins[format]]
    });

    return result[0];
  }

  async generateBlurPlaceholder(inputPath) {
    const buffer = await sharp(inputPath)
      .resize(20, 20, { fit: 'inside' })
      .blur(1)
      .jpeg({ quality: 20 })
      .toBuffer();

    return `data:image/jpeg;base64,${buffer.toString('base64')}`;
  }

  generatePictureElement(imageName, alt, className = '') {
    return `
      <picture class="${className}">
        <source 
          media="(min-width: 1280px)"
          srcset="${imageName}-1920w.avif 1920w, ${imageName}-1280w.avif 1280w"
          type="image/avif">
        <source 
          media="(min-width: 1024px)"
          srcset="${imageName}-1280w.webp 1280w, ${imageName}-1024w.webp 1024w"
          type="image/webp">
        <source 
          media="(min-width: 768px)"
          srcset="${imageName}-1024w.webp 1024w, ${imageName}-768w.webp 768w"
          type="image/webp">
        <source 
          srcset="${imageName}-768w.webp 768w, ${imageName}-640w.webp 640w, ${imageName}-320w.webp 320w"
          type="image/webp">
        <img 
          src="${imageName}-768w.jpg"
          srcset="${imageName}-320w.jpg 320w, ${imageName}-640w.jpg 640w, ${imageName}-768w.jpg 768w, ${imageName}-1024w.jpg 1024w"
          sizes="(min-width: 1024px) 1024px, (min-width: 768px) 768px, 100vw"
          alt="${alt}"
          loading="lazy"
          decoding="async">
      </picture>
    `;
  }
}
```

### CSS Optimization
```javascript
// scripts/css-optimization.js
import postcss from 'postcss';
import autoprefixer from 'autoprefixer';
import cssnano from 'cssnano';
import purgecss from '@fullhuman/postcss-purgecss';

class CSSOptimizer {
  constructor(config) {
    this.config = config;
    this.processors = this.createProcessors();
  }

  createProcessors() {
    return {
      development: [
        autoprefixer()
      ],
      
      production: [
        autoprefixer(),
        purgecss({
          content: [
            './dist/**/*.html',
            './src/js/**/*.js',
            './src/js/**/*.jsx'
          ],
          safelist: [
            /^js-/,
            /^data-/,
            'active',
            'open',
            'visible',
            'loading',
            'error',
            'success'
          ],
          defaultExtractor: content => content.match(/[\w-/:]+(?<!:)/g) || []
        }),
        cssnano({
          preset: ['advanced', {
            discardComments: { removeAll: true },
            normalizeWhitespace: true,
            colormin: true,
            convertValues: true,
            discardDuplicates: true,
            mergeLonghand: true,
            mergeRules: true,
            minifyFontValues: true,
            minifyParams: true,
            minifySelectors: true,
            normalizeCharset: true,
            normalizeDisplayValues: true,
            normalizePositions: true,
            normalizeRepeatStyle: true,
            normalizeString: true,
            normalizeTimingFunctions: true,
            normalizeUnicode: true,
            normalizeUrl: true,
            orderedValues: true,
            reduceIdents: true,
            reduceInitial: true,
            reduceTransforms: true,
            svgo: true,
            uniqueSelectors: true
          }]
        })
      ]
    };
  }

  async optimizeCSS(css, environment = 'production') {
    const processors = this.processors[environment];
    const result = await postcss(processors).process(css, { from: undefined });
    
    return {
      css: result.css,
      map: result.map,
      warnings: result.warnings()
    };
  }

  async extractCriticalCSS(htmlContent, cssContent) {
    const critical = await postcss([
      require('postcss-critical-css')({
        preserve: false,
        minify: true
      })
    ]).process(cssContent, { from: undefined });

    return critical.css;
  }

  calculateCSSMetrics(css) {
    const lines = css.split('\n').length;
    const size = Buffer.byteLength(css, 'utf8');
    const gzipSize = require('gzip-size').sync(css);
    const selectors = (css.match(/[^{}]+{/g) || []).length;
    const rules = (css.match(/{[^}]*}/g) || []).length;

    return {
      lines,
      size,
      gzipSize,
      compressionRatio: ((size - gzipSize) / size * 100).toFixed(2),
      selectors,
      rules,
      selectorsPerRule: (selectors / rules).toFixed(2)
    };
  }
}
```

### JavaScript Optimization
```javascript
// scripts/js-optimization.js
import { minify } from 'terser';
import { rollup } from 'rollup';
import { nodeResolve } from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import babel from '@rollup/plugin-babel';

class JavaScriptOptimizer {
  constructor(config) {
    this.config = config;
    this.terserOptions = this.createTerserOptions();
  }

  createTerserOptions() {
    return {
      compress: {
        drop_console: this.config.environment === 'production',
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
        passes: 2,
        unsafe_arrows: true,
        unsafe_methods: true,
        unsafe_proto: true
      },
      mangle: {
        safari10: true,
        properties: {
          regex: /^_/
        }
      },
      format: {
        comments: false,
        ascii_only: true
      },
      sourceMap: this.config.environment !== 'production'
    };
  }

  async optimizeBundle(inputPath, outputPath) {
    const bundle = await rollup({
      input: inputPath,
      plugins: [
        nodeResolve({
          browser: true,
          preferBuiltins: false
        }),
        commonjs(),
        babel({
          babelHelpers: 'bundled',
          exclude: 'node_modules/**',
          presets: [
            ['@babel/preset-env', {
              targets: 'defaults',
              modules: false
            }],
            ['@babel/preset-react', {
              runtime: 'automatic'
            }]
          ]
        })
      ],
      external: ['react', 'react-dom']
    });

    const { output } = await bundle.generate({
      format: 'es',
      sourcemap: this.config.environment !== 'production'
    });

    const code = output[0].code;
    const minified = await minify(code, this.terserOptions);

    return {
      original: code,
      minified: minified.code,
      map: minified.map,
      savings: ((code.length - minified.code.length) / code.length * 100).toFixed(2)
    };
  }

  async createServiceWorker() {
    const swContent = `
      const CACHE_NAME = 'jobhub-v${this.config.version}';
      const STATIC_ASSETS = [
        '/',
        '/assets/css/site.css',
        '/assets/js/site.js',
        '/assets/fonts/inter-regular.woff2',
        '/assets/images/logo.svg'
      ];

      self.addEventListener('install', event => {
        event.waitUntil(
          caches.open(CACHE_NAME)
            .then(cache => cache.addAll(STATIC_ASSETS))
        );
      });

      self.addEventListener('fetch', event => {
        if (event.request.destination === 'document') {
          event.respondWith(
            caches.match(event.request)
              .then(response => response || fetch(event.request))
          );
        }
      });

      self.addEventListener('activate', event => {
        event.waitUntil(
          caches.keys().then(cacheNames => {
            return Promise.all(
              cacheNames
                .filter(cacheName => cacheName !== CACHE_NAME)
                .map(cacheName => caches.delete(cacheName))
            );
          })
        );
      });
    `;

    const minified = await minify(swContent, this.terserOptions);
    return minified.code;
  }
}
```

## Performance Monitoring

### Core Web Vitals Tracking
```javascript
// src/js/performance-monitor.js
class PerformanceMonitor {
  constructor() {
    this.metrics = {};
    this.observers = [];
    this.init();
  }

  init() {
    this.measureCoreWebVitals();
    this.measureResourceTiming();
    this.measureUserTiming();
    this.setupPerformanceObserver();
  }

  measureCoreWebVitals() {
    // First Contentful Paint
    this.measureFCP();
    
    // Largest Contentful Paint
    this.measureLCP();
    
    // First Input Delay
    this.measureFID();
    
    // Cumulative Layout Shift
    this.measureCLS();
  }

  measureFCP() {
    const observer = new PerformanceObserver(list => {
      const entries = list.getEntries();
      const fcp = entries.find(entry => entry.name === 'first-contentful-paint');
      
      if (fcp) {
        this.metrics.fcp = fcp.startTime;
        this.reportMetric('FCP', fcp.startTime);
        observer.disconnect();
      }
    });

    observer.observe({ entryTypes: ['paint'] });
    this.observers.push(observer);
  }

  measureLCP() {
    const observer = new PerformanceObserver(list => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      this.metrics.lcp = lastEntry.startTime;
      this.reportMetric('LCP', lastEntry.startTime);
    });

    observer.observe({ entryTypes: ['largest-contentful-paint'] });
    this.observers.push(observer);
  }

  measureFID() {
    const observer = new PerformanceObserver(list => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        this.metrics.fid = entry.processingStart - entry.startTime;
        this.reportMetric('FID', this.metrics.fid);
      });
    });

    observer.observe({ entryTypes: ['first-input'] });
    this.observers.push(observer);
  }

  measureCLS() {
    let clsValue = 0;
    let sessionValue = 0;
    let sessionEntries = [];

    const observer = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        if (!entry.hadRecentInput) {
          const firstSessionEntry = sessionEntries[0];
          const lastSessionEntry = sessionEntries[sessionEntries.length - 1];

          if (sessionValue && 
              entry.startTime - lastSessionEntry.startTime < 1000 &&
              entry.startTime - firstSessionEntry.startTime < 5000) {
            sessionValue += entry.value;
            sessionEntries.push(entry);
          } else {
            sessionValue = entry.value;
            sessionEntries = [entry];
          }

          if (sessionValue > clsValue) {
            clsValue = sessionValue;
            this.metrics.cls = clsValue;
            this.reportMetric('CLS', clsValue);
          }
        }
      }
    });

    observer.observe({ entryTypes: ['layout-shift'] });
    this.observers.push(observer);
  }

  measureResourceTiming() {
    const resources = performance.getEntriesByType('resource');
    
    const resourceMetrics = {
      totalResources: resources.length,
      totalSize: 0,
      slowestResource: null,
      resourceTypes: {}
    };

    resources.forEach(resource => {
      const type = this.getResourceType(resource.name);
      
      if (!resourceMetrics.resourceTypes[type]) {
        resourceMetrics.resourceTypes[type] = {
          count: 0,
          totalDuration: 0,
          totalSize: 0
        };
      }

      resourceMetrics.resourceTypes[type].count++;
      resourceMetrics.resourceTypes[type].totalDuration += resource.duration;
      
      if (resource.transferSize) {
        resourceMetrics.resourceTypes[type].totalSize += resource.transferSize;
        resourceMetrics.totalSize += resource.transferSize;
      }

      if (!resourceMetrics.slowestResource || 
          resource.duration > resourceMetrics.slowestResource.duration) {
        resourceMetrics.slowestResource = resource;
      }
    });

    this.metrics.resources = resourceMetrics;
  }

  getResourceType(url) {
    if (url.includes('.css')) return 'css';
    if (url.includes('.js')) return 'js';
    if (url.match(/\.(jpg|jpeg|png|gif|webp|avif|svg)$/)) return 'image';
    if (url.match(/\.(woff|woff2|ttf|otf)$/)) return 'font';
    return 'other';
  }

  reportMetric(name, value) {
    // Report to analytics
    if (window.gtag) {
      window.gtag('event', 'web_vitals', {
        event_category: 'Performance',
        event_label: name,
        value: Math.round(value),
        custom_map: { metric_value: value }
      });
    }

    // Report to custom analytics
    if (window.analytics) {
      window.analytics.track('Performance Metric', {
        metric: name,
        value: value,
        url: window.location.pathname,
        timestamp: Date.now()
      });
    }

    // Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.log(`${name}: ${value.toFixed(2)}ms`);
    }
  }

  generateReport() {
    return {
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      } : null,
      metrics: this.metrics,
      budget: this.checkPerformanceBudget()
    };
  }

  checkPerformanceBudget() {
    const budget = {
      fcp: { limit: 1500, actual: this.metrics.fcp, passed: this.metrics.fcp < 1500 },
      lcp: { limit: 2500, actual: this.metrics.lcp, passed: this.metrics.lcp < 2500 },
      fid: { limit: 100, actual: this.metrics.fid, passed: this.metrics.fid < 100 },
      cls: { limit: 0.1, actual: this.metrics.cls, passed: this.metrics.cls < 0.1 }
    };

    const passed = Object.values(budget).every(metric => metric.passed);
    
    return {
      passed,
      metrics: budget,
      score: Object.values(budget).filter(metric => metric.passed).length / Object.keys(budget).length
    };
  }

  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Initialize performance monitoring
const performanceMonitor = new PerformanceMonitor();

// Report metrics on page unload
window.addEventListener('beforeunload', () => {
  const report = performanceMonitor.generateReport();
  
  // Send beacon for reliable reporting
  if (navigator.sendBeacon) {
    navigator.sendBeacon('/api/performance', JSON.stringify(report));
  }
  
  performanceMonitor.cleanup();
});

export default PerformanceMonitor;
```
