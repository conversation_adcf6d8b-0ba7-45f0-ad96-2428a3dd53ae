# Google AdSense Integration System

## AdSense Architecture Overview

### Strategic Ad Placement
```
Page Layout with AdSense:
├── Header Ad (728x90 Leaderboard)
├── Content Area
│   ├── Job Listings
│   ├── Inline Ads (every 5th job)
│   └── Content Ad (300x250 Rectangle)
├── Sidebar Ads (300x600 Skyscraper)
└── Footer Ad (728x90 Leaderboard)
```

### WordPress AdSense Configuration

```php
// manager/wp-content/themes/universal-theme/includes/adsense-config.php
class AdSenseConfiguration 
{
    public function __construct() 
    {
        add_action('admin_menu', [$this, 'addAdminMenu']);
        add_action('admin_init', [$this, 'registerSettings']);
        add_action('wp_head', [$this, 'addAdSenseScript']);
    }
    
    public function addAdminMenu(): void 
    {
        add_submenu_page(
            'universal-theme-settings',
            'AdSense Settings',
            'AdSense',
            'manage_options',
            'adsense-settings',
            [$this, 'renderSettingsPage']
        );
    }
    
    public function registerSettings(): void 
    {
        register_setting('adsense_settings', 'adsense_client_id');
        register_setting('adsense_settings', 'adsense_auto_ads');
        register_setting('adsense_settings', 'adsense_lazy_load');
        register_setting('adsense_settings', 'adsense_responsive');
        
        // Ad slot settings
        register_setting('adsense_settings', 'adsense_slot_header');
        register_setting('adsense_settings', 'adsense_slot_content');
        register_setting('adsense_settings', 'adsense_slot_sidebar');
        register_setting('adsense_settings', 'adsense_slot_footer');
        register_setting('adsense_settings', 'adsense_slot_inline');
        
        // Ad sizes and responsive settings
        register_setting('adsense_settings', 'adsense_sizes');
        register_setting('adsense_settings', 'adsense_breakpoints');
    }
    
    public function renderSettingsPage(): void 
    {
        ?>
        <div class="wrap">
            <h1>AdSense Configuration</h1>
            <form method="post" action="options.php">
                <?php settings_fields('adsense_settings'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">AdSense Client ID</th>
                        <td>
                            <input type="text" name="adsense_client_id" 
                                   value="<?= esc_attr(get_option('adsense_client_id')); ?>" 
                                   placeholder="ca-pub-xxxxxxxxxxxxxxxx" class="regular-text" />
                            <p class="description">Your Google AdSense publisher ID</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Header Ad Slot</th>
                        <td>
                            <input type="text" name="adsense_slot_header" 
                                   value="<?= esc_attr(get_option('adsense_slot_header')); ?>" 
                                   placeholder="1234567890" class="regular-text" />
                            <p class="description">728x90 Leaderboard ad slot ID</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Content Ad Slot</th>
                        <td>
                            <input type="text" name="adsense_slot_content" 
                                   value="<?= esc_attr(get_option('adsense_slot_content')); ?>" 
                                   placeholder="1234567890" class="regular-text" />
                            <p class="description">300x250 Rectangle ad slot ID</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Sidebar Ad Slot</th>
                        <td>
                            <input type="text" name="adsense_slot_sidebar" 
                                   value="<?= esc_attr(get_option('adsense_slot_sidebar')); ?>" 
                                   placeholder="1234567890" class="regular-text" />
                            <p class="description">300x600 Skyscraper ad slot ID</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Inline Ad Slot</th>
                        <td>
                            <input type="text" name="adsense_slot_inline" 
                                   value="<?= esc_attr(get_option('adsense_slot_inline')); ?>" 
                                   placeholder="1234567890" class="regular-text" />
                            <p class="description">Responsive inline ads between job listings</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Auto Ads</th>
                        <td>
                            <label>
                                <input type="checkbox" name="adsense_auto_ads" value="1" 
                                       <?= checked(1, get_option('adsense_auto_ads'), false); ?> />
                                Enable Google Auto Ads
                            </label>
                            <p class="description">Let Google automatically place ads on your site</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Lazy Loading</th>
                        <td>
                            <label>
                                <input type="checkbox" name="adsense_lazy_load" value="1" 
                                       <?= checked(1, get_option('adsense_lazy_load', 1), false); ?> />
                                Enable lazy loading for ads
                            </label>
                            <p class="description">Load ads only when they come into viewport</p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button('Save AdSense Settings'); ?>
            </form>
            
            <div class="adsense-preview">
                <h2>Ad Placement Preview</h2>
                <div class="preview-container">
                    <!-- Visual preview of ad placements -->
                    <div class="preview-page">
                        <div class="preview-header">Header Ad (728x90)</div>
                        <div class="preview-content">
                            <div class="preview-job">Job 1</div>
                            <div class="preview-job">Job 2</div>
                            <div class="preview-job">Job 3</div>
                            <div class="preview-job">Job 4</div>
                            <div class="preview-job">Job 5</div>
                            <div class="preview-ad-inline">Inline Ad</div>
                            <div class="preview-job">Job 6</div>
                        </div>
                        <div class="preview-sidebar">
                            <div class="preview-ad-sidebar">Sidebar Ad (300x600)</div>
                        </div>
                        <div class="preview-footer">Footer Ad (728x90)</div>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .preview-container { margin-top: 20px; border: 1px solid #ddd; padding: 20px; }
        .preview-page { display: grid; grid-template-columns: 1fr 320px; gap: 20px; }
        .preview-header, .preview-footer { grid-column: 1 / -1; background: #e3f2fd; padding: 10px; text-align: center; }
        .preview-ad-inline { background: #fff3e0; padding: 10px; margin: 10px 0; text-align: center; }
        .preview-ad-sidebar { background: #f3e5f5; padding: 20px; text-align: center; height: 200px; }
        .preview-job { background: #f5f5f5; padding: 10px; margin: 5px 0; }
        </style>
        <?php
    }
    
    public function addAdSenseScript(): void 
    {
        $clientId = get_option('adsense_client_id');
        if (!$clientId) return;
        
        $autoAds = get_option('adsense_auto_ads', false);
        
        echo '<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=' . esc_attr($clientId) . '" crossorigin="anonymous"></script>';
        
        if ($autoAds) {
            echo '<script>(adsbygoogle = window.adsbygoogle || []).push({google_ad_client: "' . esc_attr($clientId) . '", enable_page_level_ads: true});</script>';
        }
    }
}

new AdSenseConfiguration();
```

## AdSense Component System

### Responsive AdSense Component
```javascript
// src/js/components/AdSenseComponent.js
class AdSenseComponent {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            clientId: '',
            slotId: '',
            format: 'auto',
            responsive: true,
            lazyLoad: true,
            ...options
        };
        
        this.isLoaded = false;
        this.isVisible = false;
        this.observer = null;
        
        this.init();
    }
    
    init() {
        if (!this.options.clientId || !this.options.slotId) {
            console.warn('AdSense: Missing client ID or slot ID');
            return;
        }
        
        this.createAdContainer();
        
        if (this.options.lazyLoad) {
            this.setupLazyLoading();
        } else {
            this.loadAd();
        }
    }
    
    createAdContainer() {
        // Remove skeleton placeholder
        const skeleton = this.container.querySelector('.ad-skeleton');
        if (skeleton) {
            skeleton.remove();
        }
        
        // Create AdSense ad unit
        const adElement = document.createElement('ins');
        adElement.className = 'adsbygoogle';
        adElement.style.display = 'block';
        adElement.setAttribute('data-ad-client', this.options.clientId);
        adElement.setAttribute('data-ad-slot', this.options.slotId);
        
        if (this.options.responsive) {
            adElement.setAttribute('data-ad-format', 'auto');
            adElement.setAttribute('data-full-width-responsive', 'true');
        } else {
            adElement.setAttribute('data-ad-format', this.options.format);
        }
        
        // Add responsive sizing
        this.applyResponsiveSizing(adElement);
        
        this.container.appendChild(adElement);
        this.adElement = adElement;
    }
    
    applyResponsiveSizing(adElement) {
        const containerWidth = this.container.offsetWidth;
        
        // Define responsive ad sizes
        const adSizes = {
            mobile: { width: 320, height: 50 },    // Mobile banner
            tablet: { width: 728, height: 90 },    // Leaderboard
            desktop: { width: 728, height: 90 }    // Leaderboard
        };
        
        let adSize;
        if (containerWidth < 768) {
            adSize = adSizes.mobile;
        } else if (containerWidth < 1024) {
            adSize = adSizes.tablet;
        } else {
            adSize = adSizes.desktop;
        }
        
        // Set explicit dimensions to prevent layout shift
        adElement.style.width = adSize.width + 'px';
        adElement.style.height = adSize.height + 'px';
        adElement.style.minHeight = adSize.height + 'px';
    }
    
    setupLazyLoading() {
        this.observer = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.isLoaded) {
                        this.loadAd();
                        this.observer.unobserve(entry.target);
                    }
                });
            },
            { rootMargin: '100px' } // Load 100px before entering viewport
        );
        
        this.observer.observe(this.container);
    }
    
    loadAd() {
        if (this.isLoaded) return;
        
        try {
            // Ensure AdSense script is loaded
            if (typeof window.adsbygoogle === 'undefined') {
                this.loadAdSenseScript().then(() => {
                    this.pushAd();
                });
            } else {
                this.pushAd();
            }
            
            this.isLoaded = true;
            
        } catch (error) {
            console.error('AdSense loading error:', error);
            this.showFallback();
        }
    }
    
    pushAd() {
        try {
            (window.adsbygoogle = window.adsbygoogle || []).push({});
            
            // Track ad loading
            this.trackAdEvent('loaded');
            
        } catch (error) {
            console.error('AdSense push error:', error);
            this.showFallback();
        }
    }
    
    loadAdSenseScript() {
        return new Promise((resolve, reject) => {
            if (document.querySelector('script[src*="adsbygoogle.js"]')) {
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            script.async = true;
            script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${this.options.clientId}`;
            script.crossOrigin = 'anonymous';
            script.onload = resolve;
            script.onerror = reject;
            
            document.head.appendChild(script);
        });
    }
    
    showFallback() {
        // Show fallback content if ad fails to load
        this.container.innerHTML = `
            <div class="ad-fallback">
                <p>Advertisement</p>
            </div>
        `;
        
        this.trackAdEvent('fallback');
    }
    
    trackAdEvent(event) {
        if (window.gtag) {
            window.gtag('event', 'adsense_' + event, {
                event_category: 'AdSense',
                event_label: this.options.slotId,
                custom_map: { slot_id: this.options.slotId }
            });
        }
    }
    
    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }
        
        if (this.adElement && this.adElement.parentNode) {
            this.adElement.parentNode.removeChild(this.adElement);
        }
    }
}

// AdSense Manager for handling multiple ads
class AdSenseManager {
    constructor() {
        this.ads = new Map();
        this.config = null;
        
        this.loadConfig();
    }
    
    async loadConfig() {
        try {
            const response = await fetch('/api/meta/adsense-config.json');
            this.config = await response.json();
            
            // Initialize ads after config is loaded
            this.initializeAds();
            
        } catch (error) {
            console.error('Failed to load AdSense config:', error);
        }
    }
    
    initializeAds() {
        if (!this.config || !this.config.client_id) return;
        
        // Find all ad containers
        document.querySelectorAll('[data-ad-slot]').forEach(container => {
            const slotName = container.dataset.adSlot;
            const slotId = this.config.slots[slotName];
            
            if (slotId) {
                const ad = new AdSenseComponent(container, {
                    clientId: this.config.client_id,
                    slotId: slotId,
                    lazyLoad: this.config.lazy_load,
                    responsive: this.config.responsive
                });
                
                this.ads.set(container, ad);
            }
        });
    }
    
    refreshAds() {
        // Refresh all ads (useful for SPA navigation)
        this.ads.forEach(ad => {
            if (!ad.isLoaded) {
                ad.loadAd();
            }
        });
    }
    
    destroyAds() {
        // Clean up all ads
        this.ads.forEach(ad => ad.destroy());
        this.ads.clear();
    }
}

// Initialize AdSense manager
const adSenseManager = new AdSenseManager();

// Refresh ads on route changes (for SPA)
if (window.router) {
    window.router.addMiddleware(async (path) => {
        // Small delay to ensure DOM is updated
        setTimeout(() => {
            adSenseManager.initializeAds();
        }, 100);
        return true;
    });
}

export { AdSenseComponent, AdSenseManager };
```

## AdSense Template Integration

### Static Page Templates with AdSense
```html
<!-- templates/job-listing-page.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{page_title}}</title>
    
    <!-- Critical CSS with ad skeletons -->
    <style>
        /* Ad skeleton styles to prevent layout shift */
        .ad-container {
            margin: 20px 0;
            text-align: center;
        }
        
        .ad-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 4px;
            position: relative;
        }
        
        .ad-skeleton::after {
            content: 'Advertisement';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #999;
            font-size: 12px;
            font-weight: 500;
        }
        
        /* Responsive ad skeleton sizes */
        .ad-skeleton[data-ad-slot="header"] {
            width: 100%;
            max-width: 728px;
            height: 90px;
            margin: 0 auto;
        }
        
        .ad-skeleton[data-ad-slot="content"] {
            width: 100%;
            max-width: 300px;
            height: 250px;
            margin: 20px auto;
        }
        
        .ad-skeleton[data-ad-slot="sidebar"] {
            width: 100%;
            max-width: 300px;
            height: 600px;
        }
        
        .ad-skeleton[data-ad-slot^="inline"] {
            width: 100%;
            max-width: 728px;
            height: 90px;
            margin: 20px auto;
        }
        
        @media (max-width: 768px) {
            .ad-skeleton[data-ad-slot="header"],
            .ad-skeleton[data-ad-slot^="inline"] {
                max-width: 320px;
                height: 50px;
            }
            
            .ad-skeleton[data-ad-slot="content"] {
                max-width: 300px;
                height: 250px;
            }
        }
        
        @keyframes shimmer {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <!-- Navigation -->
        
        <!-- Header Ad -->
        <div class="ad-container ad-container--header">
            <div class="ad-skeleton" data-ad-slot="header"></div>
        </div>
    </header>
    
    <!-- Main Content -->
    <main class="main-content">
        <div class="content-wrapper">
            <div class="content-area">
                <h1>{{page_title}}</h1>
                
                <!-- Job Listings -->
                <div class="job-listings">
                    {{#each jobs}}
                        <article class="job-card">
                            <!-- Job content -->
                        </article>
                        
                        {{#if @index_is_multiple_of_5}}
                            <!-- Inline Ad every 5th job -->
                            <div class="ad-container ad-container--inline">
                                <div class="ad-skeleton" data-ad-slot="inline-{{math @index '/' 5}}"></div>
                            </div>
                        {{/if}}
                    {{/each}}
                </div>
                
                <!-- Content Ad -->
                <div class="ad-container ad-container--content">
                    <div class="ad-skeleton" data-ad-slot="content"></div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <aside class="sidebar">
                <!-- Sidebar content -->
                
                <!-- Sidebar Ad -->
                <div class="ad-container ad-container--sidebar">
                    <div class="ad-skeleton" data-ad-slot="sidebar"></div>
                </div>
            </aside>
        </div>
    </main>
    
    <!-- Footer -->
    <footer>
        <!-- Footer Ad -->
        <div class="ad-container ad-container--footer">
            <div class="ad-skeleton" data-ad-slot="footer"></div>
        </div>
        
        <!-- Footer content -->
    </footer>
    
    <!-- Load main CSS and JS -->
    <link rel="stylesheet" href="/assets/css/site.min.css">
    <script src="/assets/js/main.min.js"></script>
</body>
</html>
```

## Performance and Layout Shift Prevention

### Advanced Skeleton Loading System
```css
/* Advanced skeleton loading system in main CSS */
@layer components {
    .skeleton-system {
        --skeleton-color: #f0f0f0;
        --skeleton-highlight: #e0e0e0;
        --skeleton-animation-duration: 1.5s;
    }
    
    .skeleton {
        background: linear-gradient(
            90deg,
            var(--skeleton-color) 25%,
            var(--skeleton-highlight) 50%,
            var(--skeleton-color) 75%
        );
        background-size: 200% 100%;
        animation: skeleton-shimmer var(--skeleton-animation-duration) infinite;
        border-radius: 4px;
        position: relative;
        overflow: hidden;
    }
    
    .skeleton::after {
        content: attr(data-skeleton-label);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #999;
        font-size: 12px;
        font-weight: 500;
        opacity: 0.7;
    }
    
    /* Specific skeleton types */
    .skeleton--ad {
        background: linear-gradient(
            90deg,
            #fff3e0 25%,
            #ffe0b2 50%,
            #fff3e0 75%
        );
        background-size: 200% 100%;
        border: 1px solid #ffcc02;
    }
    
    .skeleton--job-card {
        height: 200px;
        margin-bottom: 20px;
    }
    
    .skeleton--text {
        height: 1em;
        margin-bottom: 0.5em;
    }
    
    .skeleton--title {
        height: 1.5em;
        width: 70%;
        margin-bottom: 1em;
    }
    
    .skeleton--image {
        aspect-ratio: 16/9;
        width: 100%;
    }
    
    /* Responsive skeleton sizes */
    @container (max-width: 768px) {
        .skeleton--ad[data-ad-slot="header"],
        .skeleton--ad[data-ad-slot^="inline"] {
            width: 320px;
            height: 50px;
        }
    }
    
    @container (min-width: 769px) {
        .skeleton--ad[data-ad-slot="header"],
        .skeleton--ad[data-ad-slot^="inline"] {
            width: 728px;
            height: 90px;
        }
    }
    
    @keyframes skeleton-shimmer {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    
    /* Fade out animation when content loads */
    .skeleton--loading-out {
        animation: skeleton-fade-out 0.3s ease-out forwards;
    }
    
    @keyframes skeleton-fade-out {
        to {
            opacity: 0;
            transform: scale(0.95);
        }
    }
}
```

This comprehensive AdSense integration system provides:

1. **WordPress Admin Interface** for easy AdSense configuration
2. **Responsive Ad Components** that prevent layout shift
3. **Advanced Skeleton Loading** with specific ad placeholders
4. **Lazy Loading** for better performance
5. **Automatic Ad Refresh** for SPA navigation
6. **Performance Tracking** and analytics integration
7. **Fallback Handling** for failed ad loads

The system ensures zero layout shift and optimal ad performance across all devices! 🚀
