# Stage 2: WordPress Theme Foundation

## Stage Overview
Create the foundational WordPress theme structure with modern PHP 8+ features, following the 150-line per file rule and modular architecture.

## Prerequisites
- Stage 1 completed successfully
- WordPress installed in manager/ directory
- PHP 8.2+ environment ready
- Composer dependencies installed

## Implementation Steps

### Step 2.1: Create Theme Header Files

#### manager/wp-content/themes/universal-theme/style.css
```css
/*
Theme Name: Universal Theme
Description: Universal modular theme for job portals and comparison sites
Version: 1.0.0
Author: Universal App Team
Text Domain: universal-theme
Domain Path: /languages
Requires at least: 6.4
Tested up to: 6.4
Requires PHP: 8.2
License: GPL v2 or later
*/

/* This file is required by WordPress but styles are compiled separately */
```

#### manager/wp-content/themes/universal-theme/index.php
```php
<?php
/**
 * Main template file
 * 
 * @package UniversalTheme
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// This theme is headless - redirect to static site
$staticSiteUrl = get_option('universal_static_site_url', '');

if ($staticSiteUrl && !is_admin()) {
    wp_redirect($staticSiteUrl . $_SERVER['REQUEST_URI'], 301);
    exit;
}

// Fallback for admin or when static site URL is not set
get_header();
?>

<main id="main" class="site-main">
    <div class="container">
        <h1>Universal Theme</h1>
        <p>This is a headless WordPress theme. Please configure the static site URL in the admin.</p>
    </div>
</main>

<?php
get_footer();
```

### Step 2.2: Create Core Theme Files

#### manager/wp-content/themes/universal-theme/functions.php
```php
<?php
/**
 * Theme functions and definitions
 * 
 * @package UniversalTheme
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define theme constants
define('UNIVERSAL_THEME_VERSION', '1.0.0');
define('UNIVERSAL_THEME_PATH', get_template_directory());
define('UNIVERSAL_THEME_URL', get_template_directory_uri());

// Autoload classes
require_once UNIVERSAL_THEME_PATH . '/vendor/autoload.php';

// Initialize theme
use UniversalApp\Theme\Core\Bootstrap;

try {
    $bootstrap = new Bootstrap();
    $bootstrap->init();
} catch (Exception $e) {
    error_log('Universal Theme Bootstrap Error: ' . $e->getMessage());
    
    // Show admin notice for errors
    if (is_admin()) {
        add_action('admin_notices', function() use ($e) {
            echo '<div class="notice notice-error"><p>Universal Theme Error: ' . esc_html($e->getMessage()) . '</p></div>';
        });
    }
}
```

#### manager/wp-content/themes/universal-theme/composer.json
```json
{
    "name": "universal-app/theme",
    "type": "wordpress-theme",
    "description": "Universal WordPress Theme",
    "require": {
        "php": ">=8.2",
        "psr/container": "^2.0",
        "psr/log": "^3.0"
    },
    "autoload": {
        "psr-4": {
            "UniversalApp\\Theme\\": "src/"
        }
    },
    "scripts": {
        "test": "phpunit",
        "lint": "phpcs --standard=PSR12 src/"
    }
}
```

### Step 2.3: Create Core Bootstrap Class

#### manager/wp-content/themes/universal-theme/src/Core/Bootstrap.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Theme\Core;

use UniversalApp\Theme\Services\ThemeService;
use UniversalApp\Theme\Services\AssetService;
use UniversalApp\Theme\Services\ConfigService;

/**
 * Theme Bootstrap Class
 * 
 * Initializes the theme and sets up core functionality
 * 
 * @package UniversalApp\Theme\Core
 * @since 1.0.0
 */
final class Bootstrap
{
    private Container $container;
    private bool $initialized = false;

    public function __construct()
    {
        $this->container = new Container();
        $this->registerServices();
    }

    /**
     * Initialize the theme
     */
    public function init(): void
    {
        if ($this->initialized) {
            return;
        }

        // WordPress hooks
        add_action('after_setup_theme', [$this, 'setupTheme']);
        add_action('wp_enqueue_scripts', [$this, 'enqueueAssets']);
        add_action('init', [$this, 'initializeServices']);
        add_action('rest_api_init', [$this, 'registerApiEndpoints']);

        // Admin hooks
        add_action('admin_menu', [$this, 'addAdminMenus']);
        add_action('admin_init', [$this, 'registerSettings']);

        $this->initialized = true;
    }

    /**
     * Setup theme features
     */
    public function setupTheme(): void
    {
        // Add theme support
        add_theme_support('post-thumbnails');
        add_theme_support('title-tag');
        add_theme_support('html5', ['search-form', 'comment-form', 'comment-list', 'gallery', 'caption']);
        add_theme_support('customize-selective-refresh-widgets');

        // Register menus
        register_nav_menus([
            'primary' => __('Primary Menu', 'universal-theme'),
            'footer' => __('Footer Menu', 'universal-theme'),
        ]);

        // Set content width
        $GLOBALS['content_width'] = 1200;
    }

    /**
     * Enqueue theme assets
     */
    public function enqueueAssets(): void
    {
        $assetService = $this->container->get(AssetService::class);
        $assetService->enqueueAssets();
    }

    /**
     * Initialize services
     */
    public function initializeServices(): void
    {
        $themeService = $this->container->get(ThemeService::class);
        $themeService->initialize();
    }

    /**
     * Register API endpoints
     */
    public function registerApiEndpoints(): void
    {
        // API endpoints will be registered here
    }

    /**
     * Add admin menus
     */
    public function addAdminMenus(): void
    {
        add_theme_page(
            __('Universal Theme Settings', 'universal-theme'),
            __('Universal Settings', 'universal-theme'),
            'manage_options',
            'universal-theme-settings',
            [$this, 'renderSettingsPage']
        );
    }

    /**
     * Register theme settings
     */
    public function registerSettings(): void
    {
        register_setting('universal_theme_settings', 'universal_static_site_url');
        register_setting('universal_theme_settings', 'universal_platform_type');
        register_setting('universal_theme_settings', 'universal_build_on_save');
    }

    /**
     * Render settings page
     */
    public function renderSettingsPage(): void
    {
        include UNIVERSAL_THEME_PATH . '/templates/admin/settings.php';
    }

    /**
     * Register services in container
     */
    private function registerServices(): void
    {
        $this->container->set(ConfigService::class, new ConfigService());
        $this->container->set(AssetService::class, new AssetService());
        $this->container->set(ThemeService::class, new ThemeService());
    }

    /**
     * Get container instance
     */
    public function getContainer(): Container
    {
        return $this->container;
    }
}
```

### Step 2.4: Create Simple DI Container

#### manager/wp-content/themes/universal-theme/src/Core/Container.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Theme\Core;

use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Container\ContainerExceptionInterface;

/**
 * Simple Dependency Injection Container
 * 
 * @package UniversalApp\Theme\Core
 * @since 1.0.0
 */
final class Container implements ContainerInterface
{
    private array $services = [];
    private array $instances = [];

    /**
     * Register a service
     */
    public function set(string $id, mixed $service): void
    {
        $this->services[$id] = $service;
    }

    /**
     * Get a service
     */
    public function get(string $id): mixed
    {
        if (!$this->has($id)) {
            throw new class("Service '{$id}' not found") extends \Exception implements NotFoundExceptionInterface {};
        }

        // Return singleton instance if already created
        if (isset($this->instances[$id])) {
            return $this->instances[$id];
        }

        $service = $this->services[$id];

        // If it's a callable, invoke it
        if (is_callable($service)) {
            $this->instances[$id] = $service($this);
        } else {
            $this->instances[$id] = $service;
        }

        return $this->instances[$id];
    }

    /**
     * Check if service exists
     */
    public function has(string $id): bool
    {
        return isset($this->services[$id]);
    }
}
```

### Step 2.5: Create Basic Services

#### manager/wp-content/themes/universal-theme/src/Services/ConfigService.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Theme\Services;

/**
 * Configuration Service
 * 
 * Handles theme configuration and settings
 * 
 * @package UniversalApp\Theme\Services
 * @since 1.0.0
 */
final class ConfigService
{
    private array $config = [];

    public function __construct()
    {
        $this->loadConfig();
    }

    /**
     * Get configuration value
     */
    public function get(string $key, mixed $default = null): mixed
    {
        return $this->config[$key] ?? $default;
    }

    /**
     * Set configuration value
     */
    public function set(string $key, mixed $value): void
    {
        $this->config[$key] = $value;
    }

    /**
     * Load configuration from WordPress options
     */
    private function loadConfig(): void
    {
        $this->config = [
            'static_site_url' => get_option('universal_static_site_url', ''),
            'platform_type' => get_option('universal_platform_type', 'job_portal'),
            'build_on_save' => get_option('universal_build_on_save', true),
            'theme_version' => UNIVERSAL_THEME_VERSION,
            'theme_path' => UNIVERSAL_THEME_PATH,
            'theme_url' => UNIVERSAL_THEME_URL,
        ];
    }

    /**
     * Save configuration to WordPress options
     */
    public function save(): void
    {
        update_option('universal_static_site_url', $this->config['static_site_url']);
        update_option('universal_platform_type', $this->config['platform_type']);
        update_option('universal_build_on_save', $this->config['build_on_save']);
    }
}
```

#### manager/wp-content/themes/universal-theme/src/Services/AssetService.php
```php
<?php
declare(strict_types=1);

namespace UniversalApp\Theme\Services;

/**
 * Asset Service
 * 
 * Handles asset loading and management
 * 
 * @package UniversalApp\Theme\Services
 * @since 1.0.0
 */
final class AssetService
{
    private string $version;

    public function __construct()
    {
        $this->version = UNIVERSAL_THEME_VERSION;
    }

    /**
     * Enqueue theme assets
     */
    public function enqueueAssets(): void
    {
        // Only enqueue in admin or when static site is not configured
        if (!is_admin() && get_option('universal_static_site_url')) {
            return;
        }

        $this->enqueueStyles();
        $this->enqueueScripts();
    }

    /**
     * Enqueue stylesheets
     */
    private function enqueueStyles(): void
    {
        wp_enqueue_style(
            'universal-theme-style',
            UNIVERSAL_THEME_URL . '/assets/css/admin.css',
            [],
            $this->version
        );
    }

    /**
     * Enqueue scripts
     */
    private function enqueueScripts(): void
    {
        wp_enqueue_script(
            'universal-theme-script',
            UNIVERSAL_THEME_URL . '/assets/js/admin.js',
            ['jquery'],
            $this->version,
            true
        );

        // Localize script
        wp_localize_script('universal-theme-script', 'universalTheme', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('universal_theme_nonce'),
            'version' => $this->version,
        ]);
    }
}
```

### Step 2.6: Create Admin Settings Template

#### manager/wp-content/themes/universal-theme/templates/admin/settings.php
```php
<?php
/**
 * Admin Settings Page Template
 * 
 * @package UniversalTheme
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$staticSiteUrl = get_option('universal_static_site_url', '');
$platformType = get_option('universal_platform_type', 'job_portal');
$buildOnSave = get_option('universal_build_on_save', true);
?>

<div class="wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    
    <form method="post" action="options.php">
        <?php
        settings_fields('universal_theme_settings');
        do_settings_sections('universal_theme_settings');
        ?>
        
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="universal_static_site_url">Static Site URL</label>
                </th>
                <td>
                    <input type="url" 
                           id="universal_static_site_url" 
                           name="universal_static_site_url" 
                           value="<?php echo esc_attr($staticSiteUrl); ?>" 
                           class="regular-text" 
                           placeholder="https://example.com" />
                    <p class="description">The URL where your static site is hosted.</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="universal_platform_type">Platform Type</label>
                </th>
                <td>
                    <select id="universal_platform_type" name="universal_platform_type">
                        <option value="job_portal" <?php selected($platformType, 'job_portal'); ?>>Job Portal</option>
                        <option value="comparison_site" <?php selected($platformType, 'comparison_site'); ?>>Comparison Site</option>
                        <option value="forex_platform" <?php selected($platformType, 'forex_platform'); ?>>Forex Platform</option>
                    </select>
                    <p class="description">Select the type of platform this site will be.</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="universal_build_on_save">Auto Build</label>
                </th>
                <td>
                    <label>
                        <input type="checkbox" 
                               id="universal_build_on_save" 
                               name="universal_build_on_save" 
                               value="1" 
                               <?php checked($buildOnSave); ?> />
                        Automatically rebuild static site when content is saved
                    </label>
                </td>
            </tr>
        </table>
        
        <?php submit_button(); ?>
    </form>
</div>
```

## Testing Checklist

### Theme Installation
- [ ] Theme appears in WordPress admin
- [ ] Theme can be activated without errors
- [ ] No PHP errors in error log
- [ ] Admin settings page is accessible
- [ ] Settings can be saved and retrieved

### Core Functionality
- [ ] Bootstrap class initializes correctly
- [ ] Container resolves services
- [ ] ConfigService loads and saves settings
- [ ] AssetService enqueues assets in admin
- [ ] Theme redirects to static site when configured

### File Structure Validation
- [ ] All PHP files follow PSR-12 standards
- [ ] No file exceeds 150 lines
- [ ] Proper namespacing is used
- [ ] Type declarations are present
- [ ] Error handling is implemented

## Success Criteria
1. ✅ WordPress theme is installable and activatable
2. ✅ Core services are working
3. ✅ Admin interface is functional
4. ✅ Configuration system works
5. ✅ Asset management is operational
6. ✅ Error handling is in place
7. ✅ Code follows PSR standards
8. ✅ All files under 150 lines

## Next Stage
Once Stage 2 is complete and all tests pass, proceed to **Stage 3: Basic Configuration System** where we'll implement the platform configuration system and JSON-based settings.
